import os
import math
import logging
import argparse
import zlib
from datetime import datetime
from typing import Optional, Callable
from iot_client.functions.ota_client import OtaClient
from iot_client.iot_client import IoTClient
from iot_client.platform.ali_mqtt_client import AmqpConfig
from iot_client.platform.emqx_mqtt_client import EMQXConfig

# 调试模式标志
DEBUG_MODE = True
logger = logging.getLogger("OTA客户端")


# 主函数
def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="OTA客户端")
    parser.add_argument("--firmware", "-f", type=str, required=not DEBUG_MODE, help="固件文件路径")
    parser.add_argument("--force", action="store_true", default=False, help="强制升级")
    parser.add_argument("--product-key", "-p", type=str, required=not DEBUG_MODE, help="产品密钥")
    parser.add_argument("--device-name", "-d", type=str, required=not DEBUG_MODE, help="设备名称")

    # 调试模式下的默认参数
    if DEBUG_MODE:
        # 创建一个命名空间对象，模拟argparse的返回值
        class Args:
            pass

        args = Args()
        args.firmware = r"C:\Users\<USER>\Desktop\WorkPlace\Charging_Pile\ECycle_G070B8\firmware\ECycle_G070B8.bin"
        args.force = True
        args.product_key = "wxd48e69e833621cfd"
        args.device_name = "100001229"

        logger.info("使用调试模式默认参数")
    else:
        args = parser.parse_args()

    # 定义进度回调函数
    def progress_callback(current, total, message):
        """
        进度回调函数，用于更新外部进度

        Args:
            current: 当前进度
            total: 总进度
            message: 当前阶段描述
        """
        if total > 0:
            percentage = (current / total) * 100
            print(f"进度: {percentage:.2f}% ({current}/{total}) - {message}")
        else:
            print(f"状态: {message}")

    # 创建配置
    config = AmqpConfig()
    exqx_config = EMQXConfig()
    # topic_filters = ["^/[^/]+/[^/]+/user/update$", "^/[^/]+/[^/]+/user/ota_ack$"]
    topic_filters = ["^/[^/]+/[^/]+/user/ota_ack$"]
    # 创建IoT客户端
    client = IoTClient(topic_filters, logger, config, exqx_config)

    # 启动客户端
    client.start()

    # 创建OTA客户端，并传入进度回调函数
    ota_client = OtaClient(client, args.product_key, args.device_name, logger, progress_callback)

    # 开始OTA升级
    success = ota_client.start_ota(args.firmware, args.force)
    logger.info(f"OTA升级结果: {success}")

    # 停止客户端
    client.stop()

    logger.info("客户端已停止")

if __name__ == "__main__":
    main()
