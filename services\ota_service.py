import os
import time
import threading
from threading import Thread, Event
from datetime import datetime
from werkzeug.utils import secure_filename
# Import Flask dependencies in each function to avoid circular imports

from models.ota_task import OtaTask
from models.device import Device
from models.firmware import Firmware
from models.database import db
from utils.logger import LoggerManager
from utils.socket_manager import emit_task_update
from services.iot_client_manager import IoTClientManager
from iot_client.functions.ota_client import OtaClient
from typing import Optional, Callable, Any
from queue import Queue, Empty
from dataclasses import dataclass

# 获取日志记录器
logger = LoggerManager.get_logger()


@dataclass
class OtaTaskInfo:
    """OTA任务信息类"""

    task_id: int
    task_func: Callable
    args: tuple
    created_at: datetime


class OtaTaskThreadManager:
    """OTA任务线程管理器

    使用生产者-消费者模式管理OTA任务：
    - 维护一个任务队列
    - 使用单个工作线程处理任务
    - 支持任务取消和状态查询
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialize()
            return cls._instance

    def _initialize(self):
        """初始化管理器"""
        self.task_queue: Queue[OtaTaskInfo] = Queue()
        self.running_tasks: dict[int, OtaTaskInfo] = {}
        self.task_events: dict[int, Event] = {}
        self.worker_thread: Optional[Thread] = None
        self.stop_event = Event()
        self._start_worker()

    def _start_worker(self):
        """启动工作线程"""
        self.worker_thread = Thread(target=self._process_tasks, daemon=True)
        self.worker_thread.start()

    def _process_tasks(self):
        """工作线程主循环

        持续从队列中获取任务并执行，直到收到停止信号
        """
        while not self.stop_event.is_set():
            task_info = None
            try:
                # 从队列中获取任务，设置超时以便能够响应停止信号
                task_info = self.task_queue.get(timeout=1)
            except Empty:
                continue
            except Exception as e:
                logger.error(f"工作线程异常: {e}")
                continue

            if task_info:
                # 更新任务状态
                self.running_tasks[task_info.task_id] = task_info

                # 执行任务
                try:
                    task_info.task_func(task_info.task_id, *task_info.args)
                except Exception as e:
                    logger.error(f"任务执行失败: {e}")
                finally:
                    # 任务完成后清理
                    self.running_tasks.pop(task_info.task_id, None)
                    self.task_events.pop(task_info.task_id, None)
                    self.task_queue.task_done()

    def add_task(self, task_func: Callable, task_id: int, *args: Any) -> None:
        """添加新的OTA任务

        Args:
            task_func: 要执行的任务函数
            task_id: 任务ID
            *args: 传递给任务函数的参数
        """
        task_info = OtaTaskInfo(task_id=task_id, task_func=task_func, args=args, created_at=datetime.now())

        # 将任务加入队列
        self.task_queue.put(task_info)
        logger.info(f"添加任务到队列: {task_id}")

    def shutdown(self):
        """关闭任务管理器"""
        self.stop_event.set()
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        logger.info("OTA任务管理器已关闭")


# 创建OTA任务管理器实例
ota_task_manager = OtaTaskThreadManager()


def run_ota_task(task_id, app=None):
    """运行OTA任务"""
    # 使用传入的app创建应用上下文
    if not app:
        logger.error(f"OTA任务 {task_id} 未提供应用实例，无法执行")
        return

    with app.app_context():
        try:
            # 获取任务和设备信息
            task = OtaTask.query.get(task_id)
            if not task:
                logger.error(f"OTA任务 {task_id} 不存在")
                return

            device = Device.query.get(task.device_id)
            if not device:
                logger.error(f"设备 {task.device_id} 不存在")
                return

            # 更新任务状态
            task.status = "进行中"
            task.progress = 0
            db.session.commit()

            # 检查IoT客户端是否已启动
            if not IoTClientManager.is_running():
                task.status = "失败"
                task.error_message = "IoT客户端未启动，请先启动客户端"
                device.last_ota_status = "失败"
                db.session.commit()

                # 发送WebSocket消息
                emit_task_update(task.id, task.status, task.progress, task.error_message)
                return

            # 定义进度回调函数，用于更新OTA任务进度
            last_emit_time = 0  # 上次发送WebSocket消息的时间

            def progress_callback(current, total, description):
                nonlocal last_emit_time
                # 计算百分比进度
                if total > 0:
                    progress = int((current / total) * 100)
                else:
                    progress = 0

                # 更新任务状态
                task.progress = progress
                task.error_message = description
                emit_task_update(task.id, task.status, task.progress, task.error_message)

                # 检查是否需要发送WebSocket消息（限制为每秒最多一次）
                current_time = time.time()
                if current_time - last_emit_time >= 2.5:  # 至少间隔n秒
                    # 发送WebSocket消息通知前端
                    last_emit_time = current_time
                    db.session.commit()
                    # 记录日志
                    logger.info(f"OTA进度更新: {progress}%, {description}")
                else:
                    # 仍然记录日志，但不发送WebSocket消息
                    logger.debug(f"OTA进度更新(未发送): {progress}%, {description}")

                return True  # 返回True表示继续操作

            # 使用全局IoT客户端
            iot_client = IoTClientManager.get_instance()

            # 创建OTA客户端
            ota_client = OtaClient(iot_client, device.product_key, device.device_id, logger, progress_callback)

            # 开始OTA升级
            success = ota_client.start_ota(task.firmware_path, force_update=True)

            # 更新任务状态
            if success:
                task.status = "成功"
                task.progress = 100
                device.last_ota_time = datetime.now()
                device.last_ota_status = "成功"
                device.firmware_version = task.firmware_version
                message = "升级成功"
            else:
                task.status = "失败"
                task.error_message = "OTA升级失败"
                device.last_ota_status = "失败"
                message = "升级失败"

            db.session.commit()

            # 发送WebSocket消息
            emit_task_update(task.id, task.status, task.progress, message)

        except Exception as e:
            logger.error(f"执行OTA任务失败: {e}")
            task.status = "失败"
            task.error_message = str(e)
            device.last_ota_status = "失败"
            db.session.commit()

            # 发送WebSocket消息
            emit_task_update(task.id, task.status, task.progress, task.error_message)

        finally:
            # 确保在多线程环境下正确清理数据库会话
            try:
                db.session.remove()
            except Exception as cleanup_error:
                logger.warning(f"清理数据库会话时出错: {cleanup_error}")


def start_ota_task(device_ids, firmware_id=None, firmware_file=None):
    """创建并启动OTA任务

    Args:
        device_ids: 设备ID列表
        firmware_id: 固件ID（与firmware_file二选一）
        firmware_file: 固件文件对象（与firmware_id二选一）

    Returns:
        (success, message): 成功状态和消息
    """
    from flask import current_app

    try:
        # 获取固件信息
        firmware_path = None
        firmware_version = None

        if firmware_id:
            # 通过固件ID获取固件信息
            firmware = Firmware.query.get(firmware_id)
            if not firmware:
                return False, "固件不存在"

            firmware_path = firmware.file_path
            firmware_version = firmware.version

        elif firmware_file:
            # 保存固件文件
            firmware_path = os.path.join(current_app.config["UPLOAD_FOLDER"], secure_filename(firmware_file.filename))
            firmware_file.save(firmware_path)

            # 读取固件版本
            firmware_version = "Unknown"
            try:
                with open(firmware_path, "rb") as f:
                    firmware_data = f.read(32)
                    if len(firmware_data) >= 32:
                        version_int = int.from_bytes(firmware_data[28:32], byteorder="little")
                        major = (version_int >> 16) & 0xFF
                        minor = (version_int >> 8) & 0xFF
                        patch = version_int & 0xFF
                        firmware_version = f"{major}.{minor}.{patch}"
            except Exception as e:
                logger.error(f"读取固件版本失败: {e}")
        else:
            return False, "未提供固件信息"

        message = f"固件版本: {firmware_version}, 固件路径: {firmware_path}"
        wait_to_oat_device_ids = set(device_ids)
        offline_device_ids = set()
        device_status_cache = current_app.config.get("DEVICE_STATUS_CACHE", {})

        # 判断当前的待升级设备的在线状态，不在线直接跳过
        # for device_id in device_ids:
        #     device = device_status_cache.get(device_id, None)
        #     if not device or not device['is_online']:
        #         wait_to_oat_device_ids.discard(device_id)
        #         offline_device_ids.add(device_id)
        message += f", 待升级设备ID: {wait_to_oat_device_ids}, 排除的不在线设备: {offline_device_ids}"
        logger.info(f"待升级设备ID: {wait_to_oat_device_ids}, 排除的不在线设备: {offline_device_ids}")

        # 判断当前待升级设备的版本号，只有小于等于固件版本号的或者版本号未知的设备才能升级
        incompatible_devices = set()
        for device_id in device_ids:
            device = Device.query.get(device_id)
            if device:
                # 如果设备当前版本未知，允许升级
                if not device.firmware_version:
                    continue

                # 比较版本号
                current_version = device.firmware_version.split(".")
                target_version = firmware_version.split(".")

                # 确保版本号格式正确
                if len(current_version) != 3 or len(target_version) != 3:
                    continue

                current_version_int = (
                    int(current_version[0]) << 16 | int(current_version[1]) << 8 | int(current_version[2])
                )
                target_version_int = int(target_version[0]) << 16 | int(target_version[1]) << 8 | int(target_version[2])
                # 比较版本号
                if current_version_int > target_version_int:
                    wait_to_oat_device_ids.discard(device_id)
                    incompatible_devices.add(device_id)

        logger.info(f"待升级设备ID: {wait_to_oat_device_ids}, 不兼容的设备: {incompatible_devices}")
        message += f", 不兼容的设备: {incompatible_devices}"

        # 创建OTA任务
        for device_id in wait_to_oat_device_ids:
            task = OtaTask(
                device_id=device_id,
                firmware_path=firmware_path,
                firmware_version=firmware_version,
                status="等待中",
                progress=0,
            )
            db.session.add(task)
            db.session.commit()
            ota_task_manager.add_task(run_ota_task, task.id, current_app._get_current_object())

        message += f", 创建的任务ID: {[device_id for device_id in wait_to_oat_device_ids]}"
        logger.info(message)
        return True, message

    except Exception as e:
        db.session.rollback()
        logger.error(f"创建OTA任务失败: {e}")
        return False, str(e)


def retry_ota_task(task_id):
    """重试OTA任务

    Args:
        task_id: 任务ID

    Returns:
        (success, message): 成功状态和消息
    """
    from flask import current_app

    try:
        # 获取任务信息
        task = OtaTask.query.get_or_404(task_id)

        # 重置任务状态
        task.status = "等待中"
        task.progress = 0
        task.error_message = ""
        task.updated_at = datetime.now()
        db.session.commit()

        ota_task_manager.add_task(run_ota_task, task.id, current_app._get_current_object())

        return True, "任务已重新开始"
    except Exception as e:
        db.session.rollback()
        logger.error(f"重试任务失败: {e}")
        return False, str(e)
