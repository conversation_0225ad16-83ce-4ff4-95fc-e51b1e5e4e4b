#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查调试数据库连接和数据
"""
import psycopg2

config = {
    'host': '**************',
    'port': 5432,
    'database': 'kfchargingdbg',
    'user': 'KfChargingDbgC',
    'password': 'JT5WJ6Zn3hbAcWBz',
    'schema': 'KfChargingDbgC_schema'
}

try:
    print("连接调试数据库...")
    conn = psycopg2.connect(
        host=config['host'],
        port=config['port'],
        database=config['database'],
        user=config['user'],
        password=config['password'],
        sslmode='disable'
    )
    cursor = conn.cursor()
    
    print("✓ 连接成功")
    
    # 检查所有可用的schema
    cursor.execute("""
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
        ORDER BY schema_name
    """)
    all_schemas = cursor.fetchall()
    print(f"可用的schema: {[s[0] for s in all_schemas]}")

    # 检查目标schema是否存在
    cursor.execute(f"""
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name = '{config['schema']}'
    """)
    schema_exists = cursor.fetchone()

    if schema_exists:
        print(f"✓ Schema {config['schema']} 存在")
        
        # 获取schema中的表
        cursor.execute(f"""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = '{config['schema']}'
            ORDER BY table_name
        """)
        tables = cursor.fetchall()
        
        print(f"Schema中包含 {len(tables)} 个表:")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {config['schema']}.{table_name}")
            count = cursor.fetchone()[0]
            print(f"  {table_name}: {count} 行")
    else:
        print(f"✗ Schema {config['schema']} 不存在")
    
    conn.close()
    
except Exception as e:
    print(f"✗ 连接失败: {e}")
