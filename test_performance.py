#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能测试脚本
用于测试设备列表和OTA任务列表的查询性能
"""

import time
import requests
import statistics
from datetime import datetime

class PerformanceTester:
    """性能测试器"""
    
    def __init__(self, base_url="http://localhost:5001"):
        """初始化测试器"""
        self.base_url = base_url
        self.session = requests.Session()
        
    def login(self, username="admin", password="admin"):
        """登录系统"""
        try:
            # 获取登录页面
            login_page = self.session.get(f"{self.base_url}/login")
            if login_page.status_code != 200:
                print(f"获取登录页面失败: {login_page.status_code}")
                return False

            # 登录
            login_data = {
                'username': username,
                'password': password
            }

            response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=True)

            # 检查是否登录成功（重定向到首页或状态码为200）
            if response.status_code == 200:
                # 尝试访问需要登录的页面来验证登录状态
                test_response = self.session.get(f"{self.base_url}/devices")
                if test_response.status_code == 200 and "设备管理" in test_response.text:
                    print("登录成功")
                    return True
                else:
                    print(f"登录验证失败: {test_response.status_code}")
                    return False
            else:
                print(f"登录失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"登录异常: {e}")
            return False
    
    def test_device_list_performance(self, iterations=10):
        """测试设备列表查询性能"""
        print("\n" + "="*50)
        print("测试设备列表查询性能")
        print("="*50)
        
        times = []
        
        for i in range(iterations):
            start_time = time.time()
            
            try:
                response = self.session.get(f"{self.base_url}/api/devices?page=1&per_page=20")
                end_time = time.time()
                
                if response.status_code == 200:
                    response_time = (end_time - start_time) * 1000  # 转换为毫秒
                    times.append(response_time)
                    
                    data = response.json()
                    device_count = len(data.get('devices', []))
                    total_count = data.get('pagination', {}).get('total', 0)
                    
                    print(f"第 {i+1} 次测试: {response_time:.2f}ms, 返回 {device_count} 个设备, 总计 {total_count} 个设备")
                else:
                    print(f"第 {i+1} 次测试失败: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"第 {i+1} 次测试异常: {e}")
            
            # 短暂延迟避免过于频繁的请求
            time.sleep(0.1)
        
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            median_time = statistics.median(times)
            
            print(f"\n设备列表查询性能统计 ({iterations} 次测试):")
            print(f"  平均响应时间: {avg_time:.2f}ms")
            print(f"  最快响应时间: {min_time:.2f}ms")
            print(f"  最慢响应时间: {max_time:.2f}ms")
            print(f"  中位数响应时间: {median_time:.2f}ms")
            
            return avg_time
        else:
            print("没有成功的测试结果")
            return None
    
    def test_ota_tasks_performance(self, iterations=10):
        """测试OTA任务列表查询性能"""
        print("\n" + "="*50)
        print("测试OTA任务列表查询性能")
        print("="*50)
        
        times = []
        
        for i in range(iterations):
            start_time = time.time()
            
            try:
                response = self.session.get(f"{self.base_url}/api/ota/tasks?page=1&per_page=20")
                end_time = time.time()
                
                if response.status_code == 200:
                    response_time = (end_time - start_time) * 1000  # 转换为毫秒
                    times.append(response_time)
                    
                    data = response.json()
                    task_count = len(data.get('tasks', []))
                    total_count = data.get('pagination', {}).get('total', 0)
                    
                    print(f"第 {i+1} 次测试: {response_time:.2f}ms, 返回 {task_count} 个任务, 总计 {total_count} 个任务")
                else:
                    print(f"第 {i+1} 次测试失败: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"第 {i+1} 次测试异常: {e}")
            
            # 短暂延迟避免过于频繁的请求
            time.sleep(0.1)
        
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            median_time = statistics.median(times)
            
            print(f"\nOTA任务列表查询性能统计 ({iterations} 次测试):")
            print(f"  平均响应时间: {avg_time:.2f}ms")
            print(f"  最快响应时间: {min_time:.2f}ms")
            print(f"  最慢响应时间: {max_time:.2f}ms")
            print(f"  中位数响应时间: {median_time:.2f}ms")
            
            return avg_time
        else:
            print("没有成功的测试结果")
            return None
    
    def test_device_list_with_filters(self, iterations=5):
        """测试带筛选条件的设备列表查询性能"""
        print("\n" + "="*50)
        print("测试带筛选条件的设备列表查询性能")
        print("="*50)
        
        test_cases = [
            {"name": "状态筛选(在线)", "params": "?status=online&page=1&per_page=20"},
            {"name": "状态筛选(离线)", "params": "?status=offline&page=1&per_page=20"},
            {"name": "产品密钥筛选", "params": "?product_key=a1&page=1&per_page=20"},
            {"name": "固件版本筛选", "params": "?firmware=v1&page=1&per_page=20"},
            {"name": "OTA状态筛选", "params": "?ota_status=success&page=1&per_page=20"},
        ]
        
        for test_case in test_cases:
            print(f"\n测试: {test_case['name']}")
            times = []
            
            for i in range(iterations):
                start_time = time.time()
                
                try:
                    response = self.session.get(f"{self.base_url}/api/devices{test_case['params']}")
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        response_time = (end_time - start_time) * 1000
                        times.append(response_time)
                        
                        data = response.json()
                        device_count = len(data.get('devices', []))
                        
                        print(f"  第 {i+1} 次: {response_time:.2f}ms, 返回 {device_count} 个设备")
                    else:
                        print(f"  第 {i+1} 次失败: HTTP {response.status_code}")
                        
                except Exception as e:
                    print(f"  第 {i+1} 次异常: {e}")
                
                time.sleep(0.1)
            
            if times:
                avg_time = statistics.mean(times)
                print(f"  平均响应时间: {avg_time:.2f}ms")
    
    def run_all_tests(self):
        """运行所有性能测试"""
        print("充电桩管理系统性能测试")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 登录
        if not self.login():
            print("登录失败，无法进行测试")
            return
        
        # 测试设备列表性能
        device_avg = self.test_device_list_performance()
        
        # 测试OTA任务列表性能
        ota_avg = self.test_ota_tasks_performance()
        
        # 测试带筛选条件的设备列表性能
        self.test_device_list_with_filters()
        
        # 总结
        print("\n" + "="*50)
        print("性能测试总结")
        print("="*50)
        
        if device_avg:
            print(f"设备列表平均响应时间: {device_avg:.2f}ms")
            if device_avg < 500:
                print("✅ 设备列表性能良好")
            elif device_avg < 1000:
                print("⚠️ 设备列表性能一般")
            else:
                print("❌ 设备列表性能需要优化")
        
        if ota_avg:
            print(f"OTA任务列表平均响应时间: {ota_avg:.2f}ms")
            if ota_avg < 500:
                print("✅ OTA任务列表性能良好")
            elif ota_avg < 1000:
                print("⚠️ OTA任务列表性能一般")
            else:
                print("❌ OTA任务列表性能需要优化")

def main():
    """主函数"""
    tester = PerformanceTester()
    tester.run_all_tests()

if __name__ == '__main__':
    main()
