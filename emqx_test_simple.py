import requests
import json
from typing import Dict, List, Optional


class EMQXClient:
    def __init__(self, host: str = "localhost", port: int = 18083, username: str = "admin", password: str = "public"):
        """
        初始化EMQX客户端

        Args:
            host: EMQX服务器地址
            port: EMQX Dashboard端口 (默认18083)
            username: Dashboard用户名
            password: Dashboard密码
        """
        self.base_url = f"http://{host}:{port}/api/v5"
        self.auth = (username, password)
        self.headers = {"Content-Type": "application/json", "Accept": "application/json"}

    def get_online_clients(self, page: int = 1, limit: int = 100) -> Optional[Dict]:
        """
        获取在线客户端列表

        Args:
            page: 页码 (从1开始)
            limit: 每页数量 (最大1000)

        Returns:
            包含客户端信息的字典，如果请求失败返回None
        """
        try:
            params = {"page": page, "limit": limit}

            response = requests.get(
                f"{self.base_url}/clients", auth=self.auth, headers=self.headers, params=params, timeout=10
            )

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"获取在线客户端失败: {e}")
            return None

    def get_client_by_id(self, client_id: str) -> Optional[Dict]:
        """
        根据client_id获取特定客户端信息

        Args:
            client_id: 客户端ID

        Returns:
            客户端信息字典，如果不存在或请求失败返回None
        """
        try:
            response = requests.get(
                f"{self.base_url}/clients/{client_id}", auth=self.auth, headers=self.headers, timeout=10
            )

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"获取客户端 {client_id} 信息失败: {e}")
            return None

    def get_all_client_ids(self) -> List[str]:
        """
        获取所有在线客户端的ID列表

        Returns:
            客户端ID列表
        """
        client_ids = []
        page = 1

        while True:
            data = self.get_online_clients(page=page, limit=100)
            if not data or "data" not in data:
                break

            clients = data["data"]
            if not clients:
                break

            # 提取client_id
            for client in clients:
                client_ids.append(client.get("clientid", ""))

            # 检查是否还有更多页
            meta = data.get("meta", {})
            total = meta.get("count", 0)
            current_count = len(client_ids)

            if current_count >= total:
                break

            page += 1

        return client_ids

    def search_clients(self, **filters) -> Optional[Dict]:
        """
        根据条件搜索客户端

        Args:
            **filters: 过滤条件，例如:
                - clientid: 客户端ID (支持模糊匹配)
                - username: 用户名
                - ip_address: IP地址
                - conn_state: 连接状态 (connected/disconnected)

        Returns:
            搜索结果字典
        """
        try:
            params = {"page": 1, "limit": 100}
            params.update(filters)

            response = requests.get(
                f"{self.base_url}/clients", auth=self.auth, headers=self.headers, params=params, timeout=10
            )

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"搜索客户端失败: {e}")
            return None

    def get_client_statistics(self) -> Optional[Dict]:
        """
        获取客户端统计信息

        Returns:
            统计信息字典
        """
        try:
            response = requests.get(f"{self.base_url}/stats", auth=self.auth, headers=self.headers, timeout=10)

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"获取统计信息失败: {e}")
            return None


def main():
    # 创建EMQX客户端实例
    emqx = EMQXClient(
        host="mqtt01.yunpusher.com",  # 替换为你的EMQX服务器地址
        port=18083,  # EMQX Dashboard端口
        username="1ca5ee6acad17589",  # Dashboard用户名
        password="nVNHMtMMFfgKVYjqq9Bvr2cUu7e8QINT5qayi6yUG0jM",  # Dashboard密码
    )

    print("=== EMQX在线设备查询 ===\n")

    # 1. 获取在线客户端列表
    print("1. 获取在线客户端列表:")
    clients_data = emqx.get_online_clients(page=1, limit=10)
    if clients_data:
        clients = clients_data.get("data", [])
        meta = clients_data.get("meta", {})
        print(f"总数: {meta.get('count', 0)}")
        print(f"当前页: {meta.get('page', 1)}")

        for client in clients:
            print(f"  Client ID: {client.get('clientid')}")
            print(f"  Username: {client.get('username', 'N/A')}")
            print(f"  IP: {client.get('ip_address', 'N/A')}")
            print(f"  连接时间: {client.get('connected_at', 'N/A')}")
            print(f"  协议: {client.get('proto_name', 'N/A')}")
            print("-" * 40)

    # 2. 获取所有客户端ID
    print("\n2. 获取所有在线客户端ID:")
    all_client_ids = emqx.get_all_client_ids()
    print(f"在线设备数量: {len(all_client_ids)}")
    for i, client_id in enumerate(all_client_ids[:5]):  # 只显示前5个
        print(f"  {i + 1}. {client_id}")
    if len(all_client_ids) > 5:
        print(f"  ... 还有 {len(all_client_ids) - 5} 个设备")

    # 3. 根据ID查询特定客户端
    if all_client_ids:
        print(f"\n3. 查询特定客户端 ({all_client_ids[0]}):")
        client_info = emqx.get_client_by_id(all_client_ids[0])
        if client_info:
            client = client_info.get("data", {})
            print(f"  Client ID: {client.get('clientid')}")
            print(f"  Username: {client.get('username', 'N/A')}")
            print(f"  IP: {client.get('ip_address', 'N/A')}")
            print(f"  端口: {client.get('port', 'N/A')}")
            print(f"  Keep Alive: {client.get('keepalive', 'N/A')}")
            print(f"  Clean Start: {client.get('clean_start', 'N/A')}")

    # 4. 搜索客户端
    print("\n4. 搜索客户端 (按连接状态):")
    search_result = emqx.search_clients(conn_state="connected")
    if search_result:
        clients = search_result.get("data", [])
        print(f"已连接的客户端数量: {len(clients)}")

    # 5. 获取统计信息
    print("\n5. 客户端统计信息:")
    stats = emqx.get_client_statistics()
    if stats:
        data = stats.get("data", {})
        print(f"  连接数: {data.get('connections.count', 0)}")
        print(f"  最大连接数: {data.get('connections.max', 0)}")
        print(f"  会话数: {data.get('sessions.count', 0)}")
        print(f"  订阅数: {data.get('subscriptions.count', 0)}")


if __name__ == "__main__":
    main()
