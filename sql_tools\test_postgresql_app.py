#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Flask应用是否能正常连接PostgreSQL数据库
"""
import os
import sys
import tempfile
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置环境变量使用调试数据库
os.environ['FLASK_ENV'] = 'development'
os.environ['DATABASE_URL'] = '****************************************************************/kfchargingdbg?options=-csearch_path%3Dkfchargingdbgc_schema'

def test_database_connection():
    """测试数据库连接"""
    try:
        print("测试数据库连接...")
        
        # 导入应用
        from app_factory import create_app
        from models.database import db
        
        # 创建应用实例
        app = create_app()
        
        with app.app_context():
            # 测试数据库连接（SQLAlchemy 2.0兼容）
            with db.engine.connect() as conn:
                result = conn.execute(db.text('SELECT 1'))
                print("✓ 数据库连接成功")
            
            # 测试查询用户表
            from models.user import User
            user_count = User.query.count()
            print(f"✓ 用户表查询成功，共 {user_count} 个用户")
            
            # 测试查询设备表
            from models.device import Device
            device_count = Device.query.count()
            print(f"✓ 设备表查询成功，共 {device_count} 个设备")
            
            # 测试查询固件表
            from models.firmware import Firmware
            firmware_count = Firmware.query.count()
            print(f"✓ 固件表查询成功，共 {firmware_count} 个固件")
            
            return True
            
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("\\n测试基本功能...")
        
        from app_factory import create_app
        from models.database import db
        from models.user import User
        
        app = create_app()
        
        with app.app_context():
            # 测试用户认证
            admin_user = User.query.filter_by(username='admin').first()
            if admin_user:
                print("✓ 管理员用户存在")
                if admin_user.check_password('admin'):
                    print("✓ 管理员密码验证成功")
                else:
                    print("✗ 管理员密码验证失败")
            else:
                print("✗ 管理员用户不存在")
            
            # 测试设备查询
            from models.device import Device
            devices = Device.query.limit(5).all()
            print(f"✓ 设备查询成功，获取到 {len(devices)} 个设备")
            
            # 测试设备参数查询
            from models.device_parameter import DeviceParameter
            params = DeviceParameter.query.limit(5).all()
            print(f"✓ 设备参数查询成功，获取到 {len(params)} 个参数")
            
            return True
            
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_web_routes():
    """测试Web路由"""
    try:
        print("\\n测试Web路由...")
        
        from app_factory import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试首页（302重定向是正常的）
            response = client.get('/')
            if response.status_code in [200, 302]:
                print("✓ 首页访问成功")
            else:
                print(f"✗ 首页访问失败，状态码: {response.status_code}")
            
            # 测试登录页面
            response = client.get('/login')
            if response.status_code == 200:
                print("✓ 登录页面访问成功")
            else:
                print(f"✗ 登录页面访问失败，状态码: {response.status_code}")
            
            # 测试API端点
            response = client.get('/api/devices')
            if response.status_code in [200, 302]:  # 可能重定向到登录页面
                print("✓ 设备API端点响应正常")
            else:
                print(f"✗ 设备API端点访问失败，状态码: {response.status_code}")
            
            return True
            
    except Exception as e:
        print(f"✗ Web路由测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    try:
        print("\\n测试数据库性能...")
        
        from app_factory import create_app
        from models.database import db
        from models.device_parameter import DeviceParameter
        import time
        
        app = create_app()
        
        with app.app_context():
            # 测试大量数据查询性能
            start_time = time.time()
            params = DeviceParameter.query.limit(1000).all()
            end_time = time.time()
            
            query_time = end_time - start_time
            print(f"✓ 查询1000条设备参数耗时: {query_time:.3f}秒")
            
            if query_time < 2.0:
                print("✓ 查询性能良好")
            else:
                print("⚠️ 查询性能较慢，可能需要优化")
            
            return True
            
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("PostgreSQL数据库应用测试")
    print("=" * 50)
    
    test_results = []
    
    # 数据库连接测试
    test_results.append(test_database_connection())
    
    # 基本功能测试
    test_results.append(test_basic_functionality())
    
    # Web路由测试
    test_results.append(test_web_routes())
    
    # 性能测试
    test_results.append(test_performance())
    
    # 总结测试结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"\\n{'='*50}")
    print(f"测试结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！PostgreSQL迁移成功！")
        print("\\n✅ 应用已准备好使用PostgreSQL数据库")
        return True
    else:
        print("❌ 部分测试失败，请检查问题")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
