#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试SQLite向后兼容性
确保应用仍然可以在SQLite环境下运行
"""

import os
import sys
from datetime import datetime

# 设置环境变量使用SQLite数据库
os.environ['FLASK_ENV'] = 'testing'
os.environ.pop('DATABASE_URL', None)  # 移除PostgreSQL配置

def test_sqlite_connection():
    """测试SQLite数据库连接"""
    try:
        print("1. 测试SQLite数据库连接...")
        
        from app_factory import create_app
        from models.database import db
        from utils.database_utils import get_database_type, get_connection_info
        
        app = create_app('testing')  # 使用测试配置
        
        with app.app_context():
            # 检查数据库类型
            db_type = get_database_type()
            print(f"   数据库类型: {db_type}")
            
            if db_type != 'sqlite':
                print(f"   ✗ 期望SQLite，但得到: {db_type}")
                return False
            
            # 获取连接信息
            conn_info = get_connection_info()
            print(f"   数据库URI: {conn_info.get('uri', 'N/A')}")
            
            # 测试基本查询
            with db.engine.connect() as conn:
                result = conn.execute(db.text('SELECT 1'))
                print("   ✓ SQLite连接成功")
            
            return True
            
    except Exception as e:
        print(f"   ✗ SQLite连接测试失败: {e}")
        return False

def test_sqlite_queries():
    """测试SQLite查询兼容性"""
    try:
        print("2. 测试SQLite查询兼容性...")
        
        from app_factory import create_app
        from models.database import db
        from models.user import User
        from utils.database_utils import date_filter
        from datetime import date
        
        app = create_app('testing')
        
        with app.app_context():
            # 创建表
            db.create_all()
            
            # 测试基本查询
            users = User.query.all()
            print(f"   查询用户: {len(users)} 个")
            
            # 测试日期查询兼容性
            today = date.today()
            try:
                # 这个查询可能没有结果，但测试语法兼容性
                filtered_users = User.query.filter(date_filter(User.created_at, today)).all()
                print(f"   日期筛选查询: {len(filtered_users)} 个用户")
            except Exception as e:
                print(f"   ✗ 日期查询失败: {e}")
                return False
            
            # 测试LIKE查询
            like_users = User.query.filter(User.username.like('%admin%')).all()
            print(f"   LIKE查询: {len(like_users)} 个用户")
            
            return True
            
    except Exception as e:
        print(f"   ✗ SQLite查询测试失败: {e}")
        return False

def test_sqlite_crud():
    """测试SQLite CRUD操作"""
    try:
        print("3. 测试SQLite CRUD操作...")
        
        from app_factory import create_app
        from models.database import db
        from models.user import User
        
        app = create_app('testing')
        
        with app.app_context():
            # 确保表存在
            db.create_all()
            
            # 创建测试用户
            test_user = User(username='sqlite_test_user', email='<EMAIL>')
            test_user.set_password('test123')
            db.session.add(test_user)
            db.session.commit()
            print("   创建用户成功")
            
            # 查询用户
            found_user = User.query.filter_by(username='sqlite_test_user').first()
            if found_user:
                print("   查询用户成功")
            else:
                print("   ✗ 查询用户失败")
                return False
            
            # 更新用户
            found_user.email = '<EMAIL>'
            db.session.commit()
            print("   更新用户成功")
            
            # 删除用户
            db.session.delete(found_user)
            db.session.commit()
            print("   删除用户成功")
            
            return True
            
    except Exception as e:
        print(f"   ✗ SQLite CRUD测试失败: {e}")
        return False

def test_configuration_switching():
    """测试配置切换"""
    try:
        print("4. 测试配置切换...")
        
        from app_factory import create_app
        from config import config
        
        # 测试不同配置
        configs_to_test = ['testing', 'development', 'production']
        
        for config_name in configs_to_test:
            try:
                app = create_app(config_name)
                with app.app_context():
                    db_uri = app.config.get('SQLALCHEMY_DATABASE_URI', '')
                    print(f"   {config_name}配置: {db_uri[:50]}...")
            except Exception as e:
                print(f"   ✗ {config_name}配置测试失败: {e}")
                return False
        
        print("   配置切换测试成功")
        return True
        
    except Exception as e:
        print(f"   ✗ 配置切换测试失败: {e}")
        return False

def test_database_utils():
    """测试数据库工具函数"""
    try:
        print("5. 测试数据库工具函数...")
        
        from app_factory import create_app
        from utils.database_utils import (
            get_database_type, 
            test_database_compatibility,
            get_database_stats
        )
        
        app = create_app('testing')
        
        with app.app_context():
            # 测试数据库类型检测
            db_type = get_database_type()
            if db_type == 'sqlite':
                print("   ✓ 数据库类型检测正确")
            else:
                print(f"   ✗ 数据库类型检测错误: {db_type}")
                return False
            
            # 测试兼容性检查
            is_compatible, message = test_database_compatibility()
            if is_compatible:
                print(f"   ✓ 兼容性测试通过: {message}")
            else:
                print(f"   ✗ 兼容性测试失败: {message}")
                return False
            
            # 测试统计信息
            stats = get_database_stats()
            if 'error' not in stats:
                print(f"   ✓ 统计信息获取成功")
            else:
                print(f"   ✗ 统计信息获取失败: {stats['error']}")
                return False
            
            return True
            
    except Exception as e:
        print(f"   ✗ 数据库工具测试失败: {e}")
        return False

def main():
    """主函数"""
    print("SQLite向后兼容性测试")
    print("=" * 50)
    
    tests = [
        test_sqlite_connection,
        test_sqlite_queries,
        test_sqlite_crud,
        test_configuration_switching,
        test_database_utils,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("   ✓ 通过\n")
            else:
                print("   ✗ 失败\n")
        except Exception as e:
            print(f"   ✗ 异常: {e}\n")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 SQLite向后兼容性测试通过！")
        print("✅ 应用可以在SQLite和PostgreSQL环境下正常运行")
        return True
    else:
        print("❌ 部分测试失败，向后兼容性有问题")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
