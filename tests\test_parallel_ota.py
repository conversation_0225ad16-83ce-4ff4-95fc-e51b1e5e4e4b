#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
并行OTA功能测试脚本
测试新的并行OTA升级功能
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_factory import create_app
from models.database import db
from models.device import Device
from models.firmware import Firmware
from models.ota_task import OtaTask
from services.parallel_ota_service import (
    initialize_parallel_ota_service, 
    start_parallel_ota_task,
    get_ota_service_stats
)
from services.parallel_ota_manager import parallel_ota_manager
from services.ota_pause_manager import ota_pause_manager
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class ParallelOtaTestSuite:
    """并行OTA测试套件"""
    
    def __init__(self):
        self.app = None
        self.test_devices = []
        self.test_firmware = None
        self.test_tasks = []
    
    def setup(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 创建测试应用
        self.app = create_app('testing')
        
        with self.app.app_context():
            # 创建数据库表
            db.create_all()
            
            # 初始化并行OTA服务
            success = initialize_parallel_ota_service(self.app, max_workers=3)
            if not success:
                raise Exception("并行OTA服务初始化失败")
            
            # 创建测试数据
            self._create_test_data()
            
        print("✅ 测试环境设置完成")
    
    def _create_test_data(self):
        """创建测试数据"""
        # 创建测试设备
        for i in range(5):
            device = Device(
                device_id=f"TEST_DEVICE_{i+1}",
                device_remark=f"测试设备{i+1}",
                product_key="test_product_key",
                firmware_version="1.0.0"
            )
            db.session.add(device)
            self.test_devices.append(device)
        
        # 创建测试固件
        firmware = Firmware(
            name="测试固件",
            version="2.0.0",
            file_path="/tmp/test_firmware.bin",
            description="用于测试的固件文件"
        )
        db.session.add(firmware)
        self.test_firmware = firmware
        
        db.session.commit()
        
        # 创建测试固件文件
        os.makedirs("/tmp", exist_ok=True)
        with open("/tmp/test_firmware.bin", "wb") as f:
            f.write(b"TEST_FIRMWARE_DATA" * 1000)  # 创建一个小的测试文件
        
        print(f"📦 创建了 {len(self.test_devices)} 个测试设备和 1 个测试固件")
    
    def test_basic_functionality(self):
        """测试基本功能"""
        print("\n🧪 测试基本功能...")
        
        with self.app.app_context():
            # 测试服务统计
            stats = get_ota_service_stats()
            print(f"📊 初始统计: {stats}")
            
            # 测试单设备OTA
            device_ids = [self.test_devices[0].id]
            success, message = start_parallel_ota_task(
                device_ids, firmware_id=self.test_firmware.id
            )
            
            if success:
                print(f"✅ 单设备OTA任务创建成功: {message}")
            else:
                print(f"❌ 单设备OTA任务创建失败: {message}")
                return False
            
            # 等待任务处理
            time.sleep(2)
            
            # 检查统计信息
            stats = get_ota_service_stats()
            print(f"📊 更新后统计: {stats}")
        
        return True
    
    def test_parallel_execution(self):
        """测试并行执行"""
        print("\n🚀 测试并行执行...")
        
        with self.app.app_context():
            # 创建多个并行任务
            device_ids = [device.id for device in self.test_devices[1:4]]  # 使用3个设备
            
            success, message = start_parallel_ota_task(
                device_ids, firmware_id=self.test_firmware.id
            )
            
            if success:
                print(f"✅ 并行OTA任务创建成功: {message}")
            else:
                print(f"❌ 并行OTA任务创建失败: {message}")
                return False
            
            # 监控任务执行
            start_time = time.time()
            max_wait_time = 30  # 最大等待30秒
            
            while time.time() - start_time < max_wait_time:
                stats = get_ota_service_stats()
                print(f"📊 执行中统计: {stats}")
                
                if stats['active_tasks'] == 0:
                    print("🎉 所有任务执行完成")
                    break
                
                time.sleep(2)
            
            # 最终统计
            final_stats = get_ota_service_stats()
            print(f"📊 最终统计: {final_stats}")
        
        return True
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n🔥 测试错误处理...")
        
        with self.app.app_context():
            # 测试无效设备ID
            invalid_device_ids = [99999]
            success, message = start_parallel_ota_task(
                invalid_device_ids, firmware_id=self.test_firmware.id
            )
            
            if not success:
                print(f"✅ 无效设备ID错误处理正确: {message}")
            else:
                print(f"❌ 无效设备ID错误处理失败: {message}")
                return False
            
            # 测试无效固件ID
            valid_device_ids = [self.test_devices[4].id]
            success, message = start_parallel_ota_task(
                valid_device_ids, firmware_id=99999
            )
            
            if not success:
                print(f"✅ 无效固件ID错误处理正确: {message}")
            else:
                print(f"❌ 无效固件ID错误处理失败: {message}")
                return False
        
        return True
    
    def test_pause_resume_functionality(self):
        """测试暂停恢复功能"""
        print("\n⏸️ 测试暂停恢复功能...")
        
        # 测试暂停管理器
        test_task_id = 12345
        
        # 测试暂停
        success = ota_pause_manager.request_pause(test_task_id)
        if success:
            print(f"✅ 任务 {test_task_id} 暂停请求成功")
        else:
            print(f"❌ 任务 {test_task_id} 暂停请求失败")
            return False
        
        # 检查暂停状态
        if ota_pause_manager.is_paused(test_task_id):
            print(f"✅ 任务 {test_task_id} 已确认暂停")
        else:
            print(f"❌ 任务 {test_task_id} 暂停状态检查失败")
            return False
        
        # 等待一秒
        time.sleep(1)
        
        # 测试恢复
        success = ota_pause_manager.request_resume(test_task_id)
        if success:
            print(f"✅ 任务 {test_task_id} 恢复请求成功")
        else:
            print(f"❌ 任务 {test_task_id} 恢复请求失败")
            return False
        
        # 检查恢复状态
        if not ota_pause_manager.is_paused(test_task_id):
            print(f"✅ 任务 {test_task_id} 已确认恢复")
        else:
            print(f"❌ 任务 {test_task_id} 恢复状态检查失败")
            return False
        
        # 获取统计信息
        stats = ota_pause_manager.get_statistics()
        print(f"📊 暂停管理器统计: {stats}")
        
        return True
    
    def test_database_concurrency(self):
        """测试数据库并发操作"""
        print("\n🗄️ 测试数据库并发操作...")
        
        def create_concurrent_task(device_id, thread_id):
            """并发创建任务的函数"""
            with self.app.app_context():
                try:
                    success, message = start_parallel_ota_task(
                        [device_id], firmware_id=self.test_firmware.id
                    )
                    print(f"🧵 线程 {thread_id}: {message}")
                    return success
                except Exception as e:
                    print(f"🧵 线程 {thread_id} 异常: {e}")
                    return False
        
        # 创建多个并发线程
        threads = []
        results = []
        
        for i in range(3):
            device_id = self.test_devices[i].id
            thread = threading.Thread(
                target=lambda did=device_id, tid=i: results.append(
                    create_concurrent_task(did, tid)
                )
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        success_count = sum(results)
        print(f"📊 并发测试结果: {success_count}/{len(threads)} 成功")
        
        return success_count > 0
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始并行OTA功能测试")
        print("=" * 50)
        
        try:
            self.setup()
            
            tests = [
                ("基本功能", self.test_basic_functionality),
                ("并行执行", self.test_parallel_execution),
                ("错误处理", self.test_error_handling),
                ("暂停恢复", self.test_pause_resume_functionality),
                ("数据库并发", self.test_database_concurrency),
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                try:
                    if test_func():
                        print(f"✅ {test_name} 测试通过")
                        passed += 1
                    else:
                        print(f"❌ {test_name} 测试失败")
                except Exception as e:
                    print(f"💥 {test_name} 测试异常: {e}")
            
            print("\n" + "=" * 50)
            print(f"🏁 测试完成: {passed}/{total} 通过")
            
            if passed == total:
                print("🎉 所有测试通过！")
                return True
            else:
                print("⚠️ 部分测试失败")
                return False
                
        except Exception as e:
            print(f"💥 测试套件异常: {e}")
            return False
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        try:
            # 停止并行OTA服务
            parallel_ota_manager.stop()
            
            # 删除测试文件
            if os.path.exists("/tmp/test_firmware.bin"):
                os.remove("/tmp/test_firmware.bin")
            
            print("✅ 测试环境清理完成")
            
        except Exception as e:
            print(f"⚠️ 清理过程中出现异常: {e}")


def main():
    """主函数"""
    test_suite = ParallelOtaTestSuite()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎊 并行OTA功能测试全部通过！")
        sys.exit(0)
    else:
        print("\n💔 并行OTA功能测试存在问题")
        sys.exit(1)


if __name__ == "__main__":
    main()
