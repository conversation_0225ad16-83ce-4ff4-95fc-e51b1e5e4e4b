#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
液态玻璃主题测试脚本
用于验证主题文件的完整性和功能
"""

import os
import sys
from pathlib import Path

def test_file_exists(file_path, description):
    """测试文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - 文件不存在")
        return False

def test_file_content(file_path, keywords, description):
    """测试文件内容是否包含关键词"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        missing_keywords = []
        for keyword in keywords:
            if keyword not in content:
                missing_keywords.append(keyword)
        
        if not missing_keywords:
            print(f"✅ {description}: 内容验证通过")
            return True
        else:
            print(f"❌ {description}: 缺少关键词 {missing_keywords}")
            return False
    except Exception as e:
        print(f"❌ {description}: 读取文件失败 - {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 液态玻璃主题完整性测试")
    print("=" * 50)
    
    # 测试结果统计
    total_tests = 0
    passed_tests = 0
    
    # 1. 测试CSS文件
    css_files = [
        ("static/css/themes/liquid-glass.css", "核心样式文件"),
        ("static/css/themes/liquid-glass-components.css", "组件样式文件"),
        ("static/css/themes/liquid-glass-homepage.css", "首页样式文件"),
        ("static/css/themes/liquid-glass-responsive.css", "响应式样式文件"),
        ("static/css/themes/liquid-glass-compatibility.css", "兼容性样式文件"),
    ]
    
    print("\n📁 CSS文件测试:")
    for file_path, description in css_files:
        total_tests += 1
        if test_file_exists(file_path, description):
            passed_tests += 1
    
    # 2. 测试JavaScript文件
    print("\n📁 JavaScript文件测试:")
    js_files = [
        ("static/js/theme-switcher.js", "主题切换器"),
    ]
    
    for file_path, description in js_files:
        total_tests += 1
        if test_file_exists(file_path, description):
            passed_tests += 1
    
    # 3. 测试模板文件
    print("\n📁 模板文件测试:")
    template_files = [
        ("templates/theme-test.html", "主题测试页面"),
    ]
    
    for file_path, description in template_files:
        total_tests += 1
        if test_file_exists(file_path, description):
            passed_tests += 1
    
    # 4. 测试文档文件
    print("\n📁 文档文件测试:")
    doc_files = [
        ("docs/liquid-glass-theme-guide.md", "详细使用指南"),
        ("README_LIQUID_GLASS_THEME.md", "快速开始指南"),
    ]
    
    for file_path, description in doc_files:
        total_tests += 1
        if test_file_exists(file_path, description):
            passed_tests += 1
    
    # 5. 测试核心功能
    print("\n🔍 核心功能测试:")
    
    # 测试主题切换器内容
    total_tests += 1
    if test_file_content(
        "static/js/theme-switcher.js",
        ["ThemeSwitcher", "liquidGlass", "switchTheme", "localStorage"],
        "主题切换器功能"
    ):
        passed_tests += 1
    
    # 测试核心CSS内容
    total_tests += 1
    if test_file_content(
        "static/css/themes/liquid-glass.css",
        ["liquid-glass-theme", "backdrop-filter", "glass-effect", "--glass-bg"],
        "核心CSS功能"
    ):
        passed_tests += 1
    
    # 测试base.html集成
    total_tests += 1
    if test_file_content(
        "templates/base.html",
        ["theme-switcher.js"],
        "base.html集成"
    ):
        passed_tests += 1
    
    # 测试路由集成
    total_tests += 1
    if test_file_content(
        "routes/main_routes.py",
        ["theme-test", "theme_test"],
        "路由集成"
    ):
        passed_tests += 1
    
    # 6. 输出测试结果
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！液态玻璃主题已成功集成。")
        print("\n🚀 下一步:")
        print("1. 启动应用: python app.py")
        print("2. 访问测试页面: http://localhost:5000/theme-test")
        print("3. 点击右下角主题切换按钮体验新主题")
        return True
    else:
        print(f"⚠️  有 {total_tests - passed_tests} 个测试失败，请检查相关文件。")
        return False

def check_browser_compatibility():
    """检查浏览器兼容性信息"""
    print("\n🌐 浏览器兼容性信息:")
    print("✅ Chrome 76+ (完全支持)")
    print("✅ Safari 14+ (完全支持)")
    print("✅ Edge 79+ (完全支持)")
    print("🔄 Firefox 103+ (部分支持，自动降级)")
    print("🔄 其他现代浏览器 (基本支持)")
    
    print("\n📱 设备优化:")
    print("✅ 桌面设备 (完整效果)")
    print("✅ 平板设备 (优化效果)")
    print("✅ 手机设备 (性能优化)")
    print("✅ 低端设备 (自动降级)")

def show_usage_tips():
    """显示使用提示"""
    print("\n💡 使用提示:")
    print("1. 主题切换: 点击右下角的主题切换按钮")
    print("2. 快捷键: Ctrl/Cmd + Shift + T")
    print("3. 调试模式: window.themeSwitcher.enableDebugMode()")
    print("4. 性能信息: window.themeSwitcher.showPerformanceInfo()")
    print("5. 测试页面: /theme-test")

if __name__ == "__main__":
    print("🎨 液态玻璃主题 - 测试和验证工具")
    print("受 iOS 26 设计系统启发的现代化 UI 主题")
    
    # 运行主测试
    success = main()
    
    # 显示额外信息
    check_browser_compatibility()
    show_usage_tips()
    
    # 退出代码
    sys.exit(0 if success else 1)
