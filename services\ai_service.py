#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI服务模块
用于集成DeepSeek API，提供智能分析功能
"""

import os
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from openai import OpenAI
from flask import current_app
from models.device import Device
from models.ota_task import OtaTask
from models.firmware import Firmware
from sqlalchemy import desc

# 获取日志记录器
logger = logging.getLogger(__name__)

class AIService:
    """AI服务类，提供各种AI功能"""
    
    def __init__(self):
        """初始化AI服务"""
        self.api_key = os.environ.get('DEEPSEEK_API_KEY', '***********************************')
        self.base_url = "https://api.deepseek.com"
        self.client = None
        
        # 初始化OpenAI客户端
        if self.api_key:
            try:
                self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)
                logger.info("DeepSeek API客户端初始化成功")
            except Exception as e:
                logger.error(f"DeepSeek API客户端初始化失败: {e}")
        else:
            logger.warning("未设置DEEPSEEK_API_KEY环境变量，AI功能将不可用")
    
    def is_available(self):
        """检查AI服务是否可用"""
        return self.client is not None
    
    def get_device_info(self, device_id=None):
        """
        获取设备信息
        
        Args:
            device_id: 设备ID，如果为None则返回所有设备
            
        Returns:
            list: 设备信息列表
        """
        try:
            if device_id:
                devices = Device.query.filter_by(id=device_id).all()
            else:
                devices = Device.query.all()
                
            device_list = []
            for device in devices:
                device_info = {
                    'id': device.id,
                    'device_id': device.device_id,
                    'product_key': device.product_key,
                    'firmware_version': device.firmware_version,
                    'last_ota_time': device.last_ota_time.strftime('%Y-%m-%d %H:%M:%S') if device.last_ota_time else None,
                    'last_ota_status': device.last_ota_status,
                    'is_online': device.is_online,
                    'last_online_time': device.last_online_time.strftime('%Y-%m-%d %H:%M:%S') if device.last_online_time else None
                }
                device_list.append(device_info)
                
            return device_list
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            return []
    
    def get_ota_tasks(self, device_id=None, limit=10):
        """
        获取OTA任务信息
        
        Args:
            device_id: 设备ID，如果为None则返回所有设备的任务
            limit: 返回的任务数量限制
            
        Returns:
            list: OTA任务信息列表
        """
        try:
            query = OtaTask.query
            if device_id:
                query = query.filter_by(device_id=device_id)
            
            tasks = query.order_by(desc(OtaTask.created_at)).limit(limit).all()
            
            task_list = []
            for task in tasks:
                task_info = {
                    'id': task.id,
                    'device_id': task.device_id,
                    'firmware_version': task.firmware_version,
                    'status': task.status,
                    'progress': task.progress,
                    'error_message': task.error_message,
                    'created_at': task.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'updated_at': task.updated_at.strftime('%Y-%m-%d %H:%M:%S')
                }
                task_list.append(task_info)
                
            return task_list
        except Exception as e:
            logger.error(f"获取OTA任务信息失败: {e}")
            return []
    
    def get_available_firmwares(self):
        """
        获取可用的固件列表
        
        Returns:
            list: 固件信息列表
        """
        try:
            firmwares = Firmware.query.order_by(desc(Firmware.version)).all()
            
            firmware_list = []
            for firmware in firmwares:
                firmware_info = {
                    'id': firmware.id,
                    'version': firmware.version,
                    'description': firmware.description,
                    'file_path': firmware.file_path,
                    'created_at': firmware.created_at.strftime('%Y-%m-%d %H:%M:%S')
                }
                firmware_list.append(firmware_info)
                
            return firmware_list
        except Exception as e:
            logger.error(f"获取固件列表失败: {e}")
            return []
    
    def get_device_status_summary(self):
        """
        获取设备状态统计信息
        
        Returns:
            dict: 设备状态统计信息
        """
        try:
            total_devices = Device.query.count()
            
            # 获取最近24小时的OTA任务统计
            recent_tasks = OtaTask.query.filter(
                OtaTask.created_at >= datetime.now() - timedelta(hours=24)
            ).all()
            
            success_tasks = sum(1 for task in recent_tasks if task.status == '成功')
            failed_tasks = sum(1 for task in recent_tasks if task.status == '失败')
            pending_tasks = sum(1 for task in recent_tasks if task.status == '等待中')
            
            return {
                'total_devices': total_devices,
                'recent_ota_tasks': {
                    'total': len(recent_tasks),
                    'success': success_tasks,
                    'failed': failed_tasks,
                    'pending': pending_tasks
                }
            }
        except Exception as e:
            logger.error(f"获取设备状态统计失败: {e}")
            return {}
    
    def analyze_device_data(self, device_data):
        """
        分析设备数据，提供智能建议
        
        Args:
            device_data: 设备数据字典
            
        Returns:
            dict: 分析结果
        """
        if not self.is_available():
            return {"error": "AI服务不可用，请检查API密钥配置"}
        
        try:
            # 构建提示信息
            prompt = f"""
            请分析以下充电桩设备数据，并提供智能建议：
            
            设备ID: {device_data.get('device_id', '未知')}
            设备状态: {device_data.get('status', '未知')}
            固件版本: {device_data.get('firmware_version', '未知')}
            最后在线时间: {device_data.get('last_online_time', '未知')}
            位置信息: {device_data.get('location', '未知')}
            
            请提供以下分析：
            1. 设备健康状况评估
            2. 是否需要固件升级
            3. 可能的故障预测
            4. 维护建议
            """
            
            # 调用DeepSeek API
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的充电桩设备分析专家，请提供专业的分析和建议。"},
                    {"role": "user", "content": prompt}
                ],
                stream=False
            )
            
            # 解析响应
            analysis = response.choices[0].message.content
            
            return {
                "success": True,
                "analysis": analysis,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            logger.error(f"设备数据分析失败: {e}")
            return {"error": f"分析失败: {str(e)}"}
    
    def predict_device_failure(self, device_history):
        """
        预测设备可能的故障
        
        Args:
            device_history: 设备历史数据列表
            
        Returns:
            dict: 预测结果
        """
        if not self.is_available():
            return {"error": "AI服务不可用，请检查API密钥配置"}
        
        try:
            # 构建提示信息
            history_text = "\n".join([f"时间: {item.get('timestamp', '')}, 状态: {item.get('status', '')}, 事件: {item.get('event', '')}" 
                                     for item in device_history])
            
            prompt = f"""
            请根据以下充电桩设备的历史数据，预测可能的故障：
            
            {history_text}
            
            请提供以下分析：
            1. 可能的故障类型
            2. 故障发生的概率
            3. 建议的预防措施
            4. 预计故障发生时间
            """
            
            # 调用DeepSeek API
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的充电桩设备故障预测专家，请提供专业的分析和建议。"},
                    {"role": "user", "content": prompt}
                ],
                stream=False
            )
            
            # 解析响应
            prediction = response.choices[0].message.content
            
            return {
                "success": True,
                "prediction": prediction,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            logger.error(f"设备故障预测失败: {e}")
            return {"error": f"预测失败: {str(e)}"}
    
    def recommend_firmware_upgrade(self, device_info, available_firmwares):
        """
        推荐固件升级
        
        Args:
            device_info: 设备信息
            available_firmwares: 可用固件列表
            
        Returns:
            dict: 推荐结果
        """
        if not self.is_available():
            return {"error": "AI服务不可用，请检查API密钥配置"}
        
        try:
            # 构建提示信息
            device_text = f"""
            设备ID: {device_info.get('device_id', '未知')}
            当前固件版本: {device_info.get('firmware_version', '未知')}
            设备类型: {device_info.get('device_type', '未知')}
            最后升级时间: {device_info.get('last_upgrade_time', '未知')}
            """
            
            firmwares_text = "\n".join([f"版本: {fw.get('version', '')}, 发布日期: {fw.get('release_date', '')}, 描述: {fw.get('description', '')}" 
                                       for fw in available_firmwares])
            
            prompt = f"""
            请根据以下充电桩设备信息和可用固件，推荐是否需要升级：
            
            设备信息:
            {device_text}
            
            可用固件:
            {firmwares_text}
            
            请提供以下分析：
            1. 是否需要升级固件
            2. 推荐升级到哪个版本
            """
            
            # 调用DeepSeek API
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的充电桩固件升级专家，请提供专业的分析和建议。"},
                    {"role": "user", "content": prompt}
                ],
                stream=False
            )
            
            # 解析响应
            recommendation = response.choices[0].message.content
            
            return {
                "success": True,
                "recommendation": recommendation,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            logger.error(f"固件升级推荐失败: {e}")
            return {"error": f"推荐失败: {str(e)}"}
    
    def chat_with_ai_assistant(self, user_message, conversation_history=None):
        """
        与AI助手聊天
        
        Args:
            user_message: 用户消息
            conversation_history: 对话历史
            
        Returns:
            dict: 聊天结果
        """
        if not self.is_available():
            return {"error": "AI服务不可用，请检查API密钥配置"}
        
        try:
            # 获取系统状态信息
            device_status = self.get_device_status_summary()
            
            # 构建系统状态提示
            system_status = f"""
            当前系统状态：
            - 总设备数：{device_status.get('total_devices', 0)}
            - 最近24小时OTA任务：
              * 总数：{device_status.get('recent_ota_tasks', {}).get('total', 0)}
              * 成功：{device_status.get('recent_ota_tasks', {}).get('success', 0)}
              * 失败：{device_status.get('recent_ota_tasks', {}).get('failed', 0)}
              * 等待中：{device_status.get('recent_ota_tasks', {}).get('pending', 0)}
            """
            
            # 构建消息列表
            messages = [
                {"role": "system", "content": f"""你是一个专业的充电桩设备管理助手，可以回答用户关于设备管理、OTA升级、故障诊断等问题。
                你可以访问以下功能：
                1. 查询设备信息和状态
                2. 查看OTA升级任务
                3. 获取可用固件列表
                4. 分析设备健康状况
                5. 提供故障诊断建议
                
                {system_status}
                
                请根据用户的问题，提供专业、准确的回答。如果用户需要查询具体信息，请告知他们可以在相应的页面查看详细信息。
                
                在回答时，请遵循以下格式规范：
                1. 使用Markdown格式
                2. 对于数据表格，使用Markdown表格语法
                3. 对于代码块，使用```语言名 代码```格式
                4. 对于重要信息，使用**加粗**或*斜体*标记
                5. 对于列表，使用有序或无序列表
                6. 对于引用，使用>引用文本
                7. 对于链接，使用[链接文本](链接地址)
                
                重要提示：
                1. 只使用系统提供的实际数据，不要虚构或猜测数据
                2. 如果用户询问的数据在系统中不存在，请明确告知"系统中没有该数据"
                3. 对于设备状态、OTA任务等数据，请使用系统提供的实际数据
                4. 不要生成虚构的设备ID、固件版本或参数值
                5. 如果用户询问具体设备信息，请告知他们可以在设备管理页面查看详细信息
                
                示例回答格式：
                # 设备状态分析
                
                ## 基本信息
                - 设备总数：**{device_status.get('total_devices', 0)}**
                - 最近24小时OTA任务：**{device_status.get('recent_ota_tasks', {}).get('total', 0)}**
                
                ## 健康状态
                > 根据系统数据，设备运行状态良好
                
                ## 详细数据
                | 参数 | 值 | 状态 |
                |------|-----|------|
                | 总设备数 | {device_status.get('total_devices', 0)} | 正常 |
                | OTA任务成功 | {device_status.get('recent_ota_tasks', {}).get('success', 0)} | 正常 |
                | OTA任务失败 | {device_status.get('recent_ota_tasks', {}).get('failed', 0)} | 正常 |
                
                ## 建议操作
                1. 继续监控设备状态
                2. 定期检查固件更新
                3. 保持设备清洁
                
                ```python
                # 示例代码
                def check_device_status(device_id):
                    return {{
                        "status": "online",
                        "last_check": "2023-01-01 12:00:00"
                    }}
                ```
                """}
            ]
            
            # 添加对话历史
            if conversation_history:
                for msg in conversation_history:
                    messages.append({"role": msg.get("role", "user"), "content": msg.get("content", "")})
            
            # 添加用户当前消息
            messages.append({"role": "user", "content": user_message})
            
            # 调用DeepSeek API
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=messages,
                stream=False
            )
            
            # 解析响应
            ai_response = response.choices[0].message.content
            
            return {
                "success": True,
                "response": ai_response,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            logger.error(f"AI助手聊天失败: {e}")
            return {"error": f"聊天失败: {str(e)}"}

    def generate_chat_response(self, user_message, conversation_history):
        """
        生成AI响应（内部方法，供API路由使用）
        
        Args:
            user_message (str): 用户消息
            conversation_history (list): 对话历史记录
            
        Returns:
            str: AI助手的响应
        """
        result = self.chat_with_ai_assistant(user_message, conversation_history)
        if "error" in result:
            return "抱歉，我现在无法正确理解您的问题，请稍后再试。"
        return result["response"]

# 创建全局AI服务实例
ai_service = AIService() 