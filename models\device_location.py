from datetime import datetime
from models.database import db

class DeviceLocation(db.Model):
    __tablename__ = 'device_locations'
    
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.String(50), db.<PERSON><PERSON><PERSON>('device.device_id'), nullable=False)
    latitude = db.Column(db.Float, nullable=False)  # 纬度
    longitude = db.Column(db.Float, nullable=False)  # 经度
    address = db.Column(db.String(200))  # 地址描述
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联设备表
    device = db.relationship('Device', backref=db.backref('location', uselist=False))
    
    def __repr__(self):
        return f'<DeviceLocation {self.device_id}>' 