<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="OTA设备管理系统 - 高效管理您的设备">
    <meta name="theme-color" content="#007bff">
    <title>{% block title %}OTA设备管理系统{% endblock %}</title>

    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://cdn.bootcdn.net" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>

    <!-- 合并的CSS文件引用 -->
    {% include 'base_css.html' %}

    <!-- 自定义CSS (使用defer加载非关键CSS) -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}"></noscript>

    <!-- 外部库 - 直接使用CDN -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- 使用国内CDN加速思源黑体 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap">
    {% block styles %}{% endblock %}
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --background-color: #f4f6f8;
            --card-background: rgba(255, 255, 255, 0.9);
        }

        body {
            padding-top: 70px;
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, var(--background-color) 0%, #e9ecef 100%);
            color: #333;
            min-height: 100vh;
        }

        /* 导航栏样式优化 */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5em;
            color: var(--primary-color) !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }
        .nav-link:hover {
            color: #0056b3 !important;
        }
        .nav-link:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }
        .footer {
            margin-top: 50px;
            padding: 20px 0;
            border-top: 1px solid #e5e5e5;
            text-align: center;
            background: #ffffff;
        }
        .device-card {
            margin-bottom: 20px;
        }
        .progress {
            margin-bottom: 0;
        }
        .nav-link:hover:after {
            width: 100%;
        }
        /* 卡片样式优化 */
        .card {
            border: none;
            border-radius: 15px;
            background: var(--card-background);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        .card-header {
            border-bottom: none;
            border-top-left-radius: 15px !important;  /* 与 card 的 border-radius 保持一致 */
            border-top-right-radius: 15px !important;
            border-bottom-left-radius: 15px !important;
            border-bottom-right-radius: 15px !important;
            background: transparent;  /* 确保背景透明，不会覆盖卡片的背景 */
        }
        /* 按钮样式优化 */
        .btn {
            border-radius: 30px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #007bff, #00bcd4);
            border: none;
            color: white;
            z-index: 1;
        }

        /* 禁用状态的按钮样式 */
        .btn:disabled {
            background: #6c757d;
            opacity: 0.65;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .btn:disabled:before {
            display: none;
        }

        .btn:disabled i {
            transform: none !important;
        }

        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #0056b3, #008ba3);
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .btn:hover:before {
            opacity: 1;
        }

        .btn:active {
            transform: translateY(1px);
        }

        /* 波纹效果 */
        .btn .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* 图标动画 */
        .btn i {
            transition: transform 0.3s ease;
        }

        .btn:hover i {
            transform: scale(1.2);
        }

        /* 特殊按钮样式 */
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .btn-success:disabled {
            background: #6c757d;
        }
        .btn-success:before {
            background: linear-gradient(45deg, #1e7e34, #1a9b7a);
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #ff9800);
        }
        .btn-warning:before {
            background: linear-gradient(45deg, #d39e00, #f57c00);
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #f44336);
        }
        .btn-danger:disabled {
            background: #6c757d;
        }
        .btn-danger:before {
            background: linear-gradient(45deg, #bd2130, #d32f2f);
        }

        /* 表格样式优化 */
        .table {
            background: var(--card-background);
            border-radius: 15px;
            overflow: hidden;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .table-hover tbody tr {
            transition: all 0.3s ease;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.1);
            transform: scale(1.01);
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* 页面切换动画 */
        .page-transition {
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 深色模式样式 */
        body.dark-mode {
            --background-color: #1a1a1a;
            --card-background: rgba(30, 30, 30, 0.9);
            --primary-color: #4dabf7;
            --secondary-color: #868e96;
            --success-color: #40c057;
            background: linear-gradient(135deg, var(--background-color) 0%, #2d2d2d 100%);
            color: #e9ecef;
        }

        body.dark-mode .navbar {
            background: rgba(30, 30, 30, 0.95) !important;
        }

        body.dark-mode .card {
            background: var(--card-background);
            color: #e9ecef;
        }

        body.dark-mode .table {
            background: var(--card-background);
            color: #e9ecef;
        }

        body.dark-mode .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(255, 255, 255, 0.05);
        }

        body.dark-mode .table-hover tbody tr:hover {
            background-color: rgba(77, 171, 247, 0.1);
        }

        body.dark-mode .btn {
            background: var(--primary-color);
            color: #fff;
        }

        body.dark-mode .nav-link {
            color: #e9ecef !important;
        }

        body.dark-mode .nav-link:hover {
            color: var(--primary-color) !important;
        }

        /* 下拉菜单样式 */
        .dropdown-menu {
            border: none;
            border-radius: 15px;
            background: var(--card-background);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 0.5rem;
            margin-top: 0.5rem;
        }

        /* 鼠标悬停时显示下拉菜单 */
        .dropdown:hover>.dropdown-menu {
            display: block;
            margin-top: 0; /* 移除间隙，防止鼠标移动时菜单消失 */
        }

        /* 修复下拉箭头 */
        .dropdown>.dropdown-toggle:active {
            pointer-events: none; /* 防止点击下拉菜单时触发下拉菜单的默认点击功能 */
        }

        .dropdown-item {
            border-radius: 10px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: rgba(0, 123, 255, 0.1);
            transform: translateX(5px);
        }

        .dropdown-item i {
            margin-right: 0.5rem;
            transition: transform 0.3s ease;
        }

        .dropdown-item:hover i {
            transform: scale(1.2);
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border-color: rgba(0,0,0,0.1);
        }

        /* 修复下拉菜单按钮的样式 */
        .nav-link.dropdown-toggle:after {
            height: 2px !important;
            border: none !important;
            margin-left: 0.5em;
        }

        /* 深色模式下的下拉菜单样式 */
        body.dark-mode .dropdown-menu {
            background: var(--card-background);
            border: 1px solid rgba(255,255,255,0.1);
        }

        body.dark-mode .dropdown-item {
            color: #e9ecef;
        }

        body.dark-mode .dropdown-item:hover {
            background: rgba(77, 171, 247, 0.1);
        }

        body.dark-mode .dropdown-divider {
            border-color: rgba(255,255,255,0.1);
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">OTA设备管理系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar" aria-controls="navbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div id="navbar" class="collapse navbar-collapse">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('device.devices') }}"><i class="fas fa-microchip"></i> 设备管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('device_location.device_map') }}"><i class="fas fa-map-marker-alt"></i> 设备地图</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('firmware.firmware_list') }}"><i class="fas fa-file-code"></i> 固件管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.ota_tasks') }}">
                            <i class="fas fa-tasks"></i> OTA任务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('iot.iot_control') }}"><i class="fas fa-server"></i> IoT客户端控制</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="moreDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-h"></i> 更多功能
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('monitor.dashboard') }}">
                                    <i class="fas fa-desktop"></i> 服务器监控
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('game.game_list') }}">
                                    <i class="fas fa-gamepad"></i> 小游戏中心
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('tools.serial') }}">
                                    <i class="fas fa-terminal"></i> Web串口工具
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('tools.toolbox') }}">
                                    <i class="fas fa-toolbox"></i> 工具箱
                                </a>
                            </li>
                            <!-- 3D模型预览入口 -->
                            <li>
                                <a class="dropdown-item" href="{{ url_for('model_viewer.model_viewer') }}">
                                    <i class="fas fa-cube"></i> 3D模型预览
                                </a>
                            </li>
                            <!-- AI功能入口 -->
                            <li class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('ai.ai_dashboard') }}">
                                    <i class="fas fa-brain"></i> AI智能分析
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('ai.chat') }}">
                                    <i class="fas fa-robot"></i> AI助手
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="https://liuyuanlin.eu.org/" target="_blank">
                                    <i class="fas fa-blog"></i> 个人博客
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="https://github.com/MisakaMikoto128" target="_blank">
                                    <i class="fab fa-github"></i> GitHub
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="{{ url_for('user.change_password') }}">
                                    <i class="fas fa-key"></i> 修改密码
                                </a>
                            </li>
                            {% if current_user.is_admin %}
                            <li>
                                <a class="dropdown-item" href="{{ url_for('user.user_list') }}">
                                    <i class="fas fa-users"></i> 用户管理
                                </a>
                            </li>
                            <li>

                                <a class="dropdown-item" href="{{ url_for('login_logs.login_logs') }}">
                                    <i class="fas fa-history"></i> 登录日志
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('paid_download.list_downloads') }}">
                                    <i class="fas fa-download"></i> 付费下载
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('paid_download.order_list') }}">
                                    <i class="fas fa-shopping-cart"></i> 我的订单
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('merchant.merchant_list') }}">
                                    <i class="fas fa-store"></i> 商户管理
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    {% if not current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">注册</a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt"></i> 退出</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p class="text-muted">© 2025 OTA设备管理系统</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='libs/js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='libs/js/bootstrap.bundle.min.js') }}"></script>
    <!-- Theme Switcher -->
    <script src="{{ url_for('static', filename='js/theme-switcher.js') }}"></script>
    <script>
        // 设备状态查询间隔（毫秒）
        var statusCheckInterval = 3000;
        var statusCheckTimer = null;

        // 获取设备状态
        function fetchDeviceStatus() {
            fetch('/api/device_status')
                .then(response => response.json())
                .then(data => {
                    console.log('获取设备状态:', data);

                    // 更新设备状态显示
                    Object.entries(data).forEach(([deviceId, status]) => {
                        // 更新状态标签
                        const statusBadge = document.querySelector(`[data-device-status="${deviceId}"]`);
                        if (statusBadge) {
                            const isOnline = status.is_online;
                            statusBadge.innerHTML = isOnline ?
                                '<i class="fas fa-circle me-1"></i>在线' :
                                '<i class="fas fa-circle me-1"></i>离线';
                            statusBadge.className = isOnline ?
                                'badge bg-success-subtle text-success' :
                                'badge bg-danger-subtle text-danger';
                        }

                        // 更新最后检查时间
                        const lastCheckSpan = document.querySelector(`[data-device-last-check="${deviceId}"]`);
                        if (lastCheckSpan) {
                            // 使用当前时间作为最后在线时间，如果设备在线
                            if (status.is_online) {
                                const now = new Date();
                                const formattedTime = now.getFullYear() + '-' +
                                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                    String(now.getDate()).padStart(2, '0') + ' ' +
                                    String(now.getHours()).padStart(2, '0') + ':' +
                                    String(now.getMinutes()).padStart(2, '0') + ':' +
                                    String(now.getSeconds()).padStart(2, '0');
                                status.last_online_time = formattedTime;
                            }
                            lastCheckSpan.innerHTML = `<i class="far fa-clock me-1"></i>${status.last_online_time || '未知'}`;
                        }

                        // 确保不会覆盖设备备注
                        // 设备备注应该保持不变，不受WebSocket更新影响
                    });

                    // 更新在线设备数量统计
                    const onlineCount = Object.values(data).filter(status => status.is_online).length;
                    const totalCount = Object.keys(data).length;

                    // 更新首页的统计卡片
                    const onlineDevicesCount = document.querySelector('#onlineDevicesCount');
                    if (onlineDevicesCount) {
                        onlineDevicesCount.textContent = onlineCount;
                    }

                    const offlineDevicesCount = document.querySelector('#offlineDevicesCount');
                    if (offlineDevicesCount) {
                        offlineDevicesCount.textContent = totalCount - onlineCount;
                    }

                    // 更新设备列表中的状态显示
                    const deviceRows = document.querySelectorAll('tbody tr');
                    deviceRows.forEach(row => {
                        const deviceId = row.querySelector('.device-checkbox')?.value;
                        if (deviceId && data[deviceId]) {
                            const status = data[deviceId];
                            // 使用data-device-status属性查找状态单元格，而不是使用固定的列索引
                            const statusCell = row.querySelector(`[data-device-status="${deviceId}"]`);
                            if (statusCell) {
                                statusCell.innerHTML = status.is_online ?
                                    '<i class="fas fa-circle me-1"></i>在线' :
                                    '<i class="fas fa-circle me-1"></i>离线';
                                statusCell.className = status.is_online ?
                                    'badge bg-success-subtle text-success' :
                                    'badge bg-danger-subtle text-danger';
                            }
                        }
                    });
                })
                .catch(error => {
                    console.error('获取设备状态失败:', error);
                    // 显示错误提示
                    const alerts = document.querySelectorAll('.alert');
                    alerts.forEach(alert => alert.remove());

                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        <i class="fas fa-exclamation-circle me-2"></i>获取设备状态失败，请检查网络连接
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
                });
        }

        // 设置状态查询间隔
        function setStatusCheckInterval(interval) {
            console.log('设置状态查询间隔:', interval);

            // 发送请求到后端API
            fetch('/api/device_status/interval', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ interval: interval })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('状态查询间隔设置成功:', data.message);
                    // 更新前端显示
                    // statusCheckInterval = statusCheckInterval;

                    // // 清除现有定时器
                    // if (statusCheckTimer) {
                    //     clearInterval(statusCheckTimer);
                    // }

                    // // 设置新的定时器
                    // statusCheckTimer = setInterval(fetchDeviceStatus, statusCheckInterval);

                    // // 立即执行一次查询
                    // fetchDeviceStatus();
                } else {
                    console.error('设置状态查询间隔失败:', data.message);
                    alert('设置状态查询间隔失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('设置状态查询间隔出错:', error);
                alert('设置状态查询间隔出错，请检查网络连接');
            });
        }

        // 获取状态查询间隔
        function getStatusCheckInterval() {
            fetch('/api/device_status/interval')
                .then(response => response.json())
                .then(data => {
                    console.log('获取状态查询间隔:', data);

                    // 更新前端显示 - 尝试查找不同的选择器ID
                    const intervalSelect = document.querySelector('#intervalSelect') || document.querySelector('#statusInterval');
                    if (intervalSelect) {
                        // 将秒数转换为对应的选项值
                        let intervalValue = '1m'; // 默认值
                        if (data.interval === 10) {
                            intervalValue = '10s';
                        } else if (data.interval === 60) {
                            intervalValue = '1m';
                        } else if (data.interval === 1800) {
                            intervalValue = '30m';
                        } else if (data.interval === 7200) {
                            intervalValue = '2h';
                        }

                        intervalSelect.value = intervalValue;

                    } else {
                        console.log('未找到状态查询间隔选择器元素');
                    }
                })
                .catch(error => {
                    console.error('获取状态查询间隔失败:', error);
                });
        }

        // 页面加载时启动状态查询
        document.addEventListener('DOMContentLoaded', function() {
            fetchDeviceStatus(statusCheckInterval);
            getStatusCheckInterval();
        });

        // 添加滚动动画
        // const animateOnScroll = () => {
        //     const elements = document.querySelectorAll('.card, .table, .btn');
        //     elements.forEach(element => {
        //         const elementTop = element.getBoundingClientRect().top;
        //         const elementBottom = element.getBoundingClientRect().bottom;

        //         if (elementTop < window.innerHeight && elementBottom > 0) {
        //             element.classList.add('animate__animated', 'animate__fadeInUp');
        //         }
        //     });
        // };

        // window.addEventListener('scroll', animateOnScroll);
        // window.addEventListener('load', animateOnScroll);
        // window.addEventListener('resize', animateOnScroll);

        // 深色模式切换现在由主题切换器处理

        // 添加波纹效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple');

                    const rect = button.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);

                    ripple.style.width = ripple.style.height = `${size}px`;
                    ripple.style.left = `${e.clientX - rect.left - size/2}px`;
                    ripple.style.top = `${e.clientY - rect.top - size/2}px`;

                    button.appendChild(ripple);

                    ripple.addEventListener('animationend', () => {
                        ripple.remove();
                    });
                });
            });
        });
    </script>
    {% block scripts %}{% endblock %}

    <!-- 彩蛋 -->
    <script>
        console.log('%c YUANLIN ', 'background: #00ff00; color: #000; font-size: 20px; font-weight: bold; padding: 10px; border-radius: 5px;');
        console.log('%c欢迎来到OTA设备管理系统！', 'color: #00ff00; font-size: 20px; font-weight: bold;');
        console.log('%c' + `
 __   __  ___   _   _  _   _  _  __  _  _
 \\ \\ / / |_ _| | | | \\| | | \\| |/  \\| \\| |
  \\ V /   | |  | |_| |\\| | |\\| | | | . \` |
   | |    | |  |  _  | | | | | | |_| |\\  |
   |_|   |___| |_| |_| |_| |_| \\___/|_| \\_|
`, 'color: #00ff00; font-family: monospace;');
        console.log('%c OTA设备管理系统已上线', 'color: #00ff00; font-size: 20px; font-weight: bold;');
        console.log('%c © 2025 OTA设备管理系统. All Rights Reserved.', 'color: #00ff00; font-size: 16px;');
    </script>
</body>
</html>