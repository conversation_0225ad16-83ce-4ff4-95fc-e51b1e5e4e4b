#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA功能详细测试脚本
测试OTA任务的创建、执行、状态更新等功能
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app_factory import create_app
from models.database import db
from models.device import Device
from models.ota_task import OtaTask
from models.firmware import Firmware
from services.parallel_ota_service import parallel_ota_service
from utils.socket_manager import emit_task_update_safe
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class OTAFunctionalityTest:
    """OTA功能测试类"""
    
    def __init__(self):
        self.app = None
        self.test_device = None
        self.test_firmware = None
        self.test_task = None
        
    def setup(self):
        """设置测试环境"""
        print("设置OTA功能测试环境...")
        
        # 创建Flask应用
        self.app = create_app()
        
        with self.app.app_context():
            # 查找或创建测试设备
            self.test_device = Device.query.filter_by(device_id='TEST_DEVICE_001').first()
            if not self.test_device:
                self.test_device = Device(
                    device_id='TEST_DEVICE_001',
                    device_name='测试设备001',
                    product_key='test_product',
                    device_secret='test_secret',
                    firmware_version='1.0.0',
                    is_online=True,
                    remark='OTA功能测试设备'
                )
                db.session.add(self.test_device)
                db.session.commit()
                print(f"✓ 创建测试设备: {self.test_device.device_id}")
            else:
                print(f"✓ 使用现有测试设备: {self.test_device.device_id}")
            
            # 查找或创建测试固件
            self.test_firmware = Firmware.query.filter_by(name='测试固件').first()
            if not self.test_firmware:
                self.test_firmware = Firmware(
                    name='测试固件',
                    version='2.0.0',
                    description='用于OTA功能测试的固件',
                    file_path='/test/firmware.bin',
                    file_size=1024000,
                    checksum='test_checksum_123'
                )
                db.session.add(self.test_firmware)
                db.session.commit()
                print(f"✓ 创建测试固件: {self.test_firmware.name} v{self.test_firmware.version}")
            else:
                print(f"✓ 使用现有测试固件: {self.test_firmware.name} v{self.test_firmware.version}")
        
        return True
    
    def cleanup(self):
        """清理测试环境"""
        print("\n清理测试环境...")
        
        with self.app.app_context():
            # 删除测试任务
            if self.test_task:
                try:
                    db.session.delete(self.test_task)
                    db.session.commit()
                    print("✓ 删除测试任务")
                except Exception as e:
                    print(f"删除测试任务失败: {e}")
            
            # 注意：不删除测试设备和固件，因为它们可能被其他测试使用
        
        print("✓ 测试环境清理完成")
    
    def test_create_ota_task(self):
        """测试创建OTA任务"""
        print("\n测试创建OTA任务...")
        
        with self.app.app_context():
            try:
                # 创建OTA任务
                self.test_task = OtaTask(
                    device_id=self.test_device.id,
                    firmware_id=self.test_firmware.id,
                    status='等待中',
                    progress=0,
                    created_by=1  # 假设用户ID为1
                )
                
                db.session.add(self.test_task)
                db.session.commit()
                
                print(f"✓ 成功创建OTA任务，ID: {self.test_task.id}")
                return True
                
            except Exception as e:
                print(f"✗ 创建OTA任务失败: {e}")
                return False
    
    def test_task_status_update(self):
        """测试任务状态更新"""
        print("\n测试任务状态更新...")
        
        if not self.test_task:
            print("✗ 没有测试任务，跳过状态更新测试")
            return False
        
        with self.app.app_context():
            try:
                # 测试不同的状态更新
                statuses = [
                    ('进行中', 25),
                    ('进行中', 50),
                    ('进行中', 75),
                    ('成功', 100)
                ]
                
                for status, progress in statuses:
                    # 更新任务状态
                    self.test_task.status = status
                    self.test_task.progress = progress
                    self.test_task.updated_at = datetime.now()
                    
                    db.session.commit()
                    
                    # 发送WebSocket更新
                    emit_task_update_safe(
                        task_id=self.test_task.id,
                        status=status,
                        progress=progress,
                        message=f"测试状态更新: {status} {progress}%"
                    )
                    
                    print(f"  ✓ 更新状态: {status} {progress}%")
                    time.sleep(1)  # 模拟处理时间
                
                return True
                
            except Exception as e:
                print(f"✗ 任务状态更新失败: {e}")
                return False
    
    def test_parallel_ota_service_integration(self):
        """测试并行OTA服务集成"""
        print("\n测试并行OTA服务集成...")
        
        if not parallel_ota_service:
            print("✗ 并行OTA服务未初始化")
            return False
        
        try:
            # 获取服务统计信息
            stats = parallel_ota_service.get_stats()
            print(f"  服务状态: {stats}")
            
            # 测试任务提交（如果有相关方法）
            if hasattr(parallel_ota_service, 'submit_task') and self.test_task:
                with self.app.app_context():
                    result = parallel_ota_service.submit_task(self.test_task.id)
                    print(f"  任务提交结果: {result}")
            
            return True
            
        except Exception as e:
            print(f"✗ 并行OTA服务集成测试失败: {e}")
            return False
    
    def test_websocket_updates(self):
        """测试WebSocket更新"""
        print("\n测试WebSocket更新...")
        
        if not self.test_task:
            print("✗ 没有测试任务，跳过WebSocket测试")
            return False
        
        try:
            # 发送多个测试更新
            test_updates = [
                {'status': '等待中', 'progress': 0, 'message': 'WebSocket测试开始'},
                {'status': '进行中', 'progress': 30, 'message': 'WebSocket测试进行中'},
                {'status': '成功', 'progress': 100, 'message': 'WebSocket测试完成'}
            ]
            
            for update in test_updates:
                emit_task_update_safe(
                    task_id=self.test_task.id,
                    status=update['status'],
                    progress=update['progress'],
                    message=update['message']
                )
                print(f"  ✓ 发送WebSocket更新: {update['message']}")
                time.sleep(0.5)
            
            return True
            
        except Exception as e:
            print(f"✗ WebSocket更新测试失败: {e}")
            return False
    
    def test_concurrent_tasks(self):
        """测试并发任务处理"""
        print("\n测试并发任务处理...")
        
        def create_and_update_task(task_name, delay):
            """创建并更新任务的线程函数"""
            with self.app.app_context():
                try:
                    # 创建任务
                    task = OtaTask(
                        device_id=self.test_device.id,
                        firmware_id=self.test_firmware.id,
                        status='等待中',
                        progress=0,
                        created_by=1,
                        remark=f'并发测试任务-{task_name}'
                    )
                    
                    db.session.add(task)
                    db.session.commit()
                    
                    print(f"    ✓ 创建并发任务 {task_name}: ID {task.id}")
                    
                    # 模拟任务执行
                    for progress in [25, 50, 75, 100]:
                        time.sleep(delay)
                        
                        task.progress = progress
                        task.status = '成功' if progress == 100 else '进行中'
                        task.updated_at = datetime.now()
                        
                        db.session.commit()
                        
                        emit_task_update_safe(
                            task_id=task.id,
                            status=task.status,
                            progress=progress,
                            message=f'并发任务 {task_name} 进度更新'
                        )
                    
                    print(f"    ✓ 完成并发任务 {task_name}")
                    
                    # 清理任务
                    db.session.delete(task)
                    db.session.commit()
                    
                except Exception as e:
                    print(f"    ✗ 并发任务 {task_name} 失败: {e}")
        
        try:
            # 创建多个并发线程
            threads = []
            for i in range(3):
                thread = threading.Thread(
                    target=create_and_update_task,
                    args=(f'Task-{i+1}', 0.5)
                )
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            print("  ✓ 并发任务测试完成")
            return True
            
        except Exception as e:
            print(f"✗ 并发任务测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有功能测试"""
        print("=" * 60)
        print("OTA功能详细测试")
        print("=" * 60)
        
        if not self.setup():
            print("测试环境设置失败")
            return False
        
        try:
            tests = [
                ("创建OTA任务", self.test_create_ota_task),
                ("任务状态更新", self.test_task_status_update),
                ("并行OTA服务集成", self.test_parallel_ota_service_integration),
                ("WebSocket更新", self.test_websocket_updates),
                ("并发任务处理", self.test_concurrent_tasks)
            ]
            
            results = []
            for test_name, test_func in tests:
                print(f"\n{'='*20} {test_name} {'='*20}")
                start_time = time.time()
                
                try:
                    result = test_func()
                    duration = time.time() - start_time
                    
                    if result:
                        print(f"✓ {test_name} - 通过 ({duration:.2f}s)")
                        results.append(('PASS', test_name, duration))
                    else:
                        print(f"✗ {test_name} - 失败 ({duration:.2f}s)")
                        results.append(('FAIL', test_name, duration))
                        
                except Exception as e:
                    duration = time.time() - start_time
                    print(f"✗ {test_name} - 异常: {e} ({duration:.2f}s)")
                    results.append(('ERROR', test_name, duration))
            
            # 输出测试摘要
            print("\n" + "=" * 60)
            print("测试结果摘要")
            print("=" * 60)
            
            total = len(results)
            passed = len([r for r in results if r[0] == 'PASS'])
            failed = len([r for r in results if r[0] == 'FAIL'])
            errors = len([r for r in results if r[0] == 'ERROR'])
            
            print(f"总测试数: {total}")
            print(f"通过: {passed}")
            print(f"失败: {failed}")
            print(f"错误: {errors}")
            print(f"成功率: {(passed/total*100):.1f}%")
            
            return passed == total
            
        finally:
            self.cleanup()


def main():
    """主函数"""
    test = OTAFunctionalityTest()
    success = test.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
