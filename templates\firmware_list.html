{% extends "base.html" %}

{% block title %}固件管理{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">固件管理</h2>
    
    <!-- 上传固件表单 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">上传新固件</h5>
        </div>
        <div class="card-body">
            <form action="{{ url_for('firmware.upload_firmware') }}" method="post" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="firmware_name" class="form-label">固件名称</label>
                            <input type="text" class="form-control" id="firmware_name" name="firmware_name" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="firmware_file" class="form-label">固件文件</label>
                            <input type="file" class="form-control" id="firmware_file" name="firmware_file" required accept=".bin">
                            <div class="form-text">支持.bin格式的固件文件，系统将自动从固件中读取版本信息</div>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="firmware_description" class="form-label">描述</label>
                    <textarea class="form-control" id="firmware_description" name="firmware_description" rows="2"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">上传固件</button>
            </form>
        </div>
    </div>

    <!-- 固件列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">固件列表</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>版本</th>
                            <th>大小</th>
                            <th>CRC32</th>
                            <th>上传时间</th>
                            <th>下载次数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for firmware in firmwares %}
                        <tr>
                            <td>{{ firmware.name }}</td>
                            <td>{{ firmware.version }}</td>
                            <td>{{ (firmware.size / 1024)|round(2) }} KB</td>
                            <td><code>{{ firmware.crc32 }}</code></td>
                            <td>{{ firmware.upload_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            <td>{{ firmware.download_count }}</td>
                            <td>
                                <a href="{{ url_for('firmware.download_firmware', id=firmware.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-download"></i> 下载
                                </a>
                                <a href="{{ url_for('firmware.delete_firmware', id=firmware.id) }}" class="btn btn-sm btn-danger" 
                                   onclick="return confirm('确定要删除这个固件吗？')">
                                    <i class="fas fa-trash"></i> 删除
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 