from iot_client.platform.ali_mqtt_client import AmqpConfig
from iot_client.platform.emqx_mqtt_client import EMQXConfig
from iot_client.platform.platform_type import PlatformType
from iot_client.iot_client import IoTClient
from iot_client.bin_block.bin_block import BinBlock
import logging
import time

# 示例用法
if __name__ == "__main__":

    # 关闭 stomp.py 的 DEBUG 日志
    logging.getLogger('stomp.py').setLevel(logging.INFO)

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(), logging.FileHandler("iot_client.log")],
    )
    logger = logging.getLogger("IoT_Client")

    # 创建配置
    config = AmqpConfig()
    exqx_config = EMQXConfig()
    # topic_filters = ["^/[^/]+/[^/]+/user/update$", "^/[^/]+/[^/]+/user/ota_ack$"]
    topic_filters = ["^/[^/]+/[^/]+/user/ota_ack$"]
    # 创建IoT客户端
    client = IoTClient(topic_filters, logger, config, exqx_config)

    # 启动客户端
    client.start()
    time.sleep(1)
    # 编码读取命令
    bin_block = BinBlock.encode_debug_info_query(100001229, 0)

    # 发送消息
    result = client.request_syc(
        "/wxd48e69e833621cfd/100001229/user/ota",
        bin_block,
        PlatformType.EMQX,
    )
    print(result)

    result = client.request_syc(
        "/wxd48e69e833621cfd/100001229/user/ota",
        bin_block,
        PlatformType.EMQX,
    )

    print(result)


    bin_block = BinBlock.encode_debug_info_query(100001241, 0)

    # 发送消息
    result = client.request_syc(
        "/hs7eigK8Xvl/100001241/user/ota",
        bin_block,
        PlatformType.ALIBABA_CLOUD,
    )

    print(result)

    # 保持程序运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在停止...")
        client.stop()
        print("已停止")
