#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
并行OTA功能测试脚本
用于验证并行OTA系统的各项功能
"""

import sys
import os
import time
import requests
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app_factory import create_app
from models.database import db
from models.device import Device
from models.ota_task import OtaTask
from models.firmware import Firmware
from services.parallel_ota_service import parallel_ota_service
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class ParallelOTATestSuite:
    """并行OTA测试套件"""
    
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.app = None
        self.test_results = []
        self.session = requests.Session()
        
    def setup(self):
        """测试环境设置"""
        print("=" * 60)
        print("并行OTA功能测试套件")
        print("=" * 60)
        
        # 创建Flask应用
        self.app = create_app()
        
        print("✓ 测试环境初始化完成")
        return True
    
    def cleanup(self):
        """清理测试环境"""
        print("\n清理测试环境...")
        # 这里可以添加清理逻辑
        print("✓ 测试环境清理完成")
    
    def run_test(self, test_name, test_func):
        """运行单个测试"""
        print(f"\n运行测试: {test_name}")
        start_time = time.time()
        
        try:
            result = test_func()
            duration = time.time() - start_time
            
            if result:
                print(f"✓ {test_name} - 通过 ({duration:.2f}s)")
                self.test_results.append({'name': test_name, 'status': 'PASS', 'duration': duration})
            else:
                print(f"✗ {test_name} - 失败 ({duration:.2f}s)")
                self.test_results.append({'name': test_name, 'status': 'FAIL', 'duration': duration})
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"✗ {test_name} - 异常: {e} ({duration:.2f}s)")
            self.test_results.append({'name': test_name, 'status': 'ERROR', 'duration': duration, 'error': str(e)})
    
    def test_database_connection(self):
        """测试数据库连接"""
        try:
            with self.app.app_context():
                # 测试数据库连接
                device_count = Device.query.count()
                firmware_count = Firmware.query.count()
                task_count = OtaTask.query.count()
                
                print(f"  - 设备数量: {device_count}")
                print(f"  - 固件数量: {firmware_count}")
                print(f"  - 任务数量: {task_count}")
                
                return True
        except Exception as e:
            print(f"  数据库连接失败: {e}")
            return False
    
    def test_parallel_ota_service(self):
        """测试并行OTA服务"""
        try:
            with self.app.app_context():
                # 检查服务是否可用
                if parallel_ota_service is None:
                    print("  并行OTA服务未初始化")
                    return False
                
                # 获取服务状态
                stats = parallel_ota_service.get_stats()
                print(f"  - 活跃任务数: {stats.get('active_tasks', 0)}")
                print(f"  - 等待任务数: {stats.get('pending_tasks', 0)}")
                print(f"  - 工作线程数: {stats.get('worker_threads', 0)}")
                
                return True
        except Exception as e:
            print(f"  并行OTA服务测试失败: {e}")
            return False
    
    def test_websocket_connection(self):
        """测试WebSocket连接"""
        try:
            # 测试WebSocket测试接口
            response = self.session.get(f"{self.base_url}/api/websocket/test")
            
            if response.status_code == 401:
                print("  需要登录，跳过WebSocket测试")
                return True
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("  ✓ WebSocket测试接口可用")
                    return True
                else:
                    print(f"  WebSocket测试失败: {data.get('message')}")
                    return False
            else:
                print(f"  WebSocket测试接口返回错误: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  WebSocket连接测试失败: {e}")
            return False
    
    def test_ota_api_endpoints(self):
        """测试OTA API端点"""
        try:
            endpoints = [
                '/api/ota/tasks',
                '/api/ota/stats',
            ]
            
            for endpoint in endpoints:
                response = self.session.get(f"{self.base_url}{endpoint}")
                
                if response.status_code == 401:
                    print(f"  {endpoint}: 需要登录 (正常)")
                elif response.status_code == 200:
                    print(f"  {endpoint}: 可访问")
                else:
                    print(f"  {endpoint}: 返回状态码 {response.status_code}")
            
            return True
            
        except Exception as e:
            print(f"  API端点测试失败: {e}")
            return False
    
    def test_database_migration_tools(self):
        """测试数据库迁移工具"""
        try:
            # 检查迁移工具文件是否存在
            migration_files = [
                'sql_tools/ota_task_migration.sql',
                'sql_tools/ota_task_auto_migration.py',
                'sql_tools/check_ota_compatibility.py'
            ]
            
            for file_path in migration_files:
                if os.path.exists(file_path):
                    print(f"  ✓ {file_path} 存在")
                else:
                    print(f"  ✗ {file_path} 不存在")
                    return False
            
            return True
            
        except Exception as e:
            print(f"  数据库迁移工具测试失败: {e}")
            return False
    
    def test_service_files(self):
        """测试服务文件"""
        try:
            service_files = [
                'services/parallel_ota_manager.py',
                'services/ota_task_executor.py',
                'services/ota_task_state.py',
                'services/ota_error_handler.py',
                'services/parallel_ota_service.py'
            ]
            
            for file_path in service_files:
                if os.path.exists(file_path):
                    print(f"  ✓ {file_path} 存在")
                else:
                    print(f"  ✗ {file_path} 不存在")
                    return False
            
            return True
            
        except Exception as e:
            print(f"  服务文件测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        if not self.setup():
            print("测试环境设置失败")
            return False
        
        try:
            # 运行各项测试
            self.run_test("数据库连接测试", self.test_database_connection)
            self.run_test("并行OTA服务测试", self.test_parallel_ota_service)
            self.run_test("WebSocket连接测试", self.test_websocket_connection)
            self.run_test("OTA API端点测试", self.test_ota_api_endpoints)
            self.run_test("数据库迁移工具测试", self.test_database_migration_tools)
            self.run_test("服务文件测试", self.test_service_files)
            
            # 输出测试结果
            self.print_test_summary()
            
        finally:
            self.cleanup()
    
    def print_test_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print("测试结果摘要")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        error_tests = len([r for r in self.test_results if r['status'] == 'ERROR'])
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"错误: {error_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0 or error_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if result['status'] in ['FAIL', 'ERROR']:
                    print(f"  - {result['name']}: {result['status']}")
                    if 'error' in result:
                        print(f"    错误: {result['error']}")


def main():
    """主函数"""
    test_suite = ParallelOTATestSuite()
    test_suite.run_all_tests()


if __name__ == "__main__":
    main()
