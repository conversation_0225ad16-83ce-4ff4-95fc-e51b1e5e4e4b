from datetime import datetime
from models.database import db

class DeviceParameter(db.Model):
    """设备参数模型，用于存储读取到的设备参数"""
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON><PERSON>('device.id'), nullable=False)
    param_name = db.Column(db.String(50), nullable=False)
    param_value = db.Column(db.String(100))
    description = db.Column(db.String(200))
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<DeviceParameter {self.param_name}: {self.param_value}>'
