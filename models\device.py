from datetime import datetime
from models.database import db

class Device(db.Model):
    """设备模型"""
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.String(80), unique=True, nullable=False)  # 设备唯一标识符
    device_remark = db.Column(db.String(200))  # 设备备注
    product_key = db.Column(db.String(80), nullable=False)
    firmware_version = db.Column(db.String(20), default="未知")
    last_ota_time = db.Column(db.DateTime, default=None)
    last_ota_status = db.Column(db.String(20), default="未升级")
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)