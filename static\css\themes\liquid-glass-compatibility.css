/* 
 * Liquid Glass Compatibility & Performance
 * Browser compatibility and performance optimizations
 */

/* ===== Feature Detection & Fallbacks ===== */

/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur(1px)) and not (-webkit-backdrop-filter: blur(1px)) {
    body.liquid-glass-theme .glass-effect,
    body.liquid-glass-theme .glass-effect-strong,
    body.liquid-glass-theme .navbar,
    body.liquid-glass-theme .card,
    body.liquid-glass-theme .dropdown-menu,
    body.liquid-glass-theme .modal-content {
        background: rgba(255, 255, 255, 0.9) !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }
    
    body.liquid-glass-theme.dark-mode .glass-effect,
    body.liquid-glass-theme.dark-mode .glass-effect-strong,
    body.liquid-glass-theme.dark-mode .navbar,
    body.liquid-glass-theme.dark-mode .card,
    body.liquid-glass-theme.dark-mode .dropdown-menu,
    body.liquid-glass-theme.dark-mode .modal-content {
        background: rgba(30, 30, 30, 0.9) !important;
    }
}

/* Fallback for browsers that don't support CSS Grid */
@supports not (display: grid) {
    body.liquid-glass-theme .grid-container {
        display: flex;
        flex-wrap: wrap;
    }
    
    body.liquid-glass-theme .grid-item {
        flex: 1 1 300px;
        margin: 10px;
    }
}

/* Fallback for browsers that don't support CSS Custom Properties */
@supports not (--css: variables) {
    body.liquid-glass-theme {
        /* Static fallback values */
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    body.liquid-glass-theme .glass-effect {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    body.liquid-glass-theme .btn {
        background: rgba(0, 123, 255, 0.8);
        border: 1px solid rgba(0, 123, 255, 0.3);
        border-radius: 25px;
        transition: all 0.3s ease;
    }
    
    body.liquid-glass-theme .card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
}

/* ===== Performance Optimizations ===== */

/* Reduce animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
    body.liquid-glass-theme *,
    body.liquid-glass-theme *::before,
    body.liquid-glass-theme *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    body.liquid-glass-theme::before,
    body.liquid-glass-theme::after,
    body.liquid-glass-theme .hero-section::before {
        animation: none !important;
    }
}

/* Performance mode for low-end devices */
body.liquid-glass-theme.performance-mode {
    /* Disable complex backgrounds */
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    background-attachment: fixed;
}

body.liquid-glass-theme.performance-mode::before,
body.liquid-glass-theme.performance-mode::after {
    display: none !important;
}

body.liquid-glass-theme.performance-mode .glass-effect,
body.liquid-glass-theme.performance-mode .glass-effect-strong {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    background: rgba(255, 255, 255, 0.8) !important;
}

body.liquid-glass-theme.performance-mode .card:hover,
body.liquid-glass-theme.performance-mode .btn:hover,
body.liquid-glass-theme.performance-mode .quick-action-card:hover {
    transform: none !important;
}

/* GPU acceleration for better performance */
body.liquid-glass-theme .card,
body.liquid-glass-theme .btn,
body.liquid-glass-theme .navbar,
body.liquid-glass-theme .dropdown-menu {
    will-change: transform, opacity;
    transform: translateZ(0); /* Force GPU acceleration */
}

/* Optimize animations for 60fps */
@keyframes optimizedFloat {
    0%, 100% { 
        transform: translate3d(0, 0, 0); 
    }
    50% { 
        transform: translate3d(0, -10px, 0); 
    }
}

@keyframes optimizedSpin {
    0% { 
        transform: rotate3d(0, 0, 1, 0deg); 
    }
    100% { 
        transform: rotate3d(0, 0, 1, 360deg); 
    }
}

/* ===== Browser-Specific Fixes ===== */

/* Safari specific fixes */
@supports (-webkit-appearance: none) {
    body.liquid-glass-theme .glass-effect {
        -webkit-backdrop-filter: var(--glass-blur);
        backdrop-filter: var(--glass-blur);
    }
    
    /* Fix Safari scrolling issues */
    body.liquid-glass-theme {
        -webkit-overflow-scrolling: touch;
    }
    
    /* Fix Safari border-radius with backdrop-filter */
    body.liquid-glass-theme .card,
    body.liquid-glass-theme .btn,
    body.liquid-glass-theme .dropdown-menu {
        -webkit-mask: radial-gradient(white, white);
        mask: radial-gradient(white, white);
    }
}

/* Firefox specific fixes */
@-moz-document url-prefix() {
    body.liquid-glass-theme .glass-effect {
        /* Firefox doesn't support backdrop-filter yet */
        background: rgba(255, 255, 255, 0.8) !important;
        backdrop-filter: none !important;
    }
    
    body.liquid-glass-theme.dark-mode .glass-effect {
        background: rgba(30, 30, 30, 0.8) !important;
    }
    
    /* Fix Firefox scrollbar styling */
    body.liquid-glass-theme {
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    }
}

/* Edge specific fixes */
@supports (-ms-ime-align: auto) {
    body.liquid-glass-theme .glass-effect {
        background: rgba(255, 255, 255, 0.8) !important;
        backdrop-filter: none !important;
    }
}

/* Internet Explorer fallback (if needed) */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    body.liquid-glass-theme {
        background: #667eea !important;
    }
    
    body.liquid-glass-theme .glass-effect,
    body.liquid-glass-theme .card,
    body.liquid-glass-theme .btn {
        background: rgba(255, 255, 255, 0.9) !important;
        filter: none !important;
    }
    
    body.liquid-glass-theme .btn:hover,
    body.liquid-glass-theme .card:hover {
        transform: none !important;
    }
}

/* ===== Mobile Browser Optimizations ===== */

/* iOS Safari specific */
@supports (-webkit-touch-callout: none) {
    body.liquid-glass-theme {
        /* Fix iOS Safari viewport issues */
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
    }
    
    body.liquid-glass-theme .btn,
    body.liquid-glass-theme .card {
        /* Prevent iOS zoom on focus */
        -webkit-user-select: none;
        user-select: none;
    }
    
    /* Fix iOS Safari backdrop-filter performance */
    body.liquid-glass-theme .glass-effect {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
}

/* Android Chrome specific */
@media screen and (max-width: 768px) and (-webkit-min-device-pixel-ratio: 1) {
    body.liquid-glass-theme .glass-effect {
        /* Reduce blur on Android for better performance */
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
}

/* ===== Accessibility Enhancements ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
    body.liquid-glass-theme {
        background: #000 !important;
        color: #fff !important;
    }
    
    body.liquid-glass-theme .glass-effect,
    body.liquid-glass-theme .card,
    body.liquid-glass-theme .btn {
        background: #fff !important;
        color: #000 !important;
        border: 2px solid #000 !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }
    
    body.liquid-glass-theme .btn:hover,
    body.liquid-glass-theme .card:hover {
        background: #f0f0f0 !important;
    }
}

/* Dark mode preference */
@media (prefers-color-scheme: dark) {
    body.liquid-glass-theme:not(.light-mode) {
        --glass-bg: rgba(30, 30, 30, 0.3);
        --glass-bg-light: rgba(30, 30, 30, 0.4);
        --glass-border: rgba(255, 255, 255, 0.1);
    }
}

/* Light mode preference */
@media (prefers-color-scheme: light) {
    body.liquid-glass-theme:not(.dark-mode) {
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-bg-light: rgba(255, 255, 255, 0.15);
        --glass-border: rgba(255, 255, 255, 0.2);
    }
}

/* ===== Print Styles ===== */
@media print {
    body.liquid-glass-theme {
        background: white !important;
        color: black !important;
    }
    
    body.liquid-glass-theme *,
    body.liquid-glass-theme *::before,
    body.liquid-glass-theme *::after {
        background: transparent !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
        box-shadow: none !important;
        text-shadow: none !important;
        animation: none !important;
        transition: none !important;
    }
    
    body.liquid-glass-theme .glass-effect,
    body.liquid-glass-theme .card,
    body.liquid-glass-theme .btn {
        border: 1px solid #000 !important;
        background: white !important;
    }
    
    /* Hide non-essential elements */
    body.liquid-glass-theme .theme-switcher-container,
    body.liquid-glass-theme .navbar,
    body.liquid-glass-theme .footer {
        display: none !important;
    }
}

/* ===== Memory and CPU Optimizations ===== */

/* Limit the number of concurrent animations */
body.liquid-glass-theme .card:nth-child(n+10):hover,
body.liquid-glass-theme .btn:nth-child(n+10):hover {
    animation: none;
    transition: opacity 0.2s ease;
}

/* Optimize for low memory devices */
@media (max-device-memory: 2) {
    body.liquid-glass-theme::before,
    body.liquid-glass-theme::after {
        display: none !important;
    }
    
    body.liquid-glass-theme .glass-effect {
        backdrop-filter: blur(5px) !important;
        -webkit-backdrop-filter: blur(5px) !important;
    }
    
    body.liquid-glass-theme .card:hover,
    body.liquid-glass-theme .btn:hover {
        transform: none !important;
    }
}

/* Optimize for slow connections */
@media (max-bandwidth: 1mbps) {
    body.liquid-glass-theme {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }
    
    body.liquid-glass-theme::before,
    body.liquid-glass-theme::after {
        display: none !important;
    }
}

/* ===== Debug Mode ===== */
body.liquid-glass-theme.debug-mode {
    /* Show performance information */
    position: relative;
}

body.liquid-glass-theme.debug-mode::after {
    content: 'Liquid Glass Theme - Debug Mode';
    position: fixed;
    top: 10px;
    left: 10px;
    background: rgba(255, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 10000;
    pointer-events: none;
}
