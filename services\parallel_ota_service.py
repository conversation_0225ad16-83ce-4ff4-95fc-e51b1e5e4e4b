#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
并行OTA服务模块
提供多设备OTA任务的并行执行功能
"""

import os
from datetime import datetime
from werkzeug.utils import secure_filename
from typing import Tuple, List, Optional

from models.ota_task import OtaTask
from models.device import Device
from models.firmware import Firmware
from models.database import db
from utils.logger import LoggerManager
from services.parallel_ota_manager import parallel_ota_manager
from services.ota_task_executor import create_ota_task_executor
from services.database_session_manager import db_session_manager, thread_safe_db_ops
from services.ota_error_handler import ota_error_handler
from services.ota_utils import (
    FirmwareValidator, DeviceValidator, OtaTaskHelper, FileManager
)
from services.iot_client_manager import IoTClientManager

# 获取日志记录器
logger = LoggerManager.get_logger()


def initialize_parallel_ota_service(app, max_workers: int = 5):
    """初始化并行OTA服务"""
    try:
        # 配置并行管理器
        parallel_ota_manager.max_workers = max_workers
        
        # 启动管理器
        parallel_ota_manager.start(app)
        
        logger.info(f"并行OTA服务初始化成功，最大并发数: {max_workers}")
        return True
        
    except Exception as e:
        logger.error(f"并行OTA服务初始化失败: {e}")
        return False


def shutdown_parallel_ota_service():
    """关闭并行OTA服务"""
    try:
        parallel_ota_manager.stop()
        logger.info("并行OTA服务已关闭")
    except Exception as e:
        logger.error(f"关闭并行OTA服务失败: {e}")


def start_parallel_ota_task(device_ids: List[int], firmware_id: int = None, 
                           firmware_file=None) -> Tuple[bool, str]:
    """创建并启动并行OTA任务
    
    Args:
        device_ids: 设备ID列表
        firmware_id: 固件ID（与firmware_file二选一）
        firmware_file: 固件文件对象（与firmware_id二选一）
    
    Returns:
        (success, message): 成功状态和消息
    """
    try:
        # 验证参数
        if not device_ids:
            return False, "设备ID列表不能为空"
        
        if not firmware_id and not firmware_file:
            return False, "必须提供固件ID或固件文件"
        
        # 处理固件信息
        firmware_info = _process_firmware_info(firmware_id, firmware_file)
        if not firmware_info:
            return False, "固件信息处理失败"
        
        firmware_path, firmware_version = firmware_info
        
        # 验证设备
        valid_devices = _validate_devices(device_ids, firmware_version)
        if not valid_devices:
            return False, "没有有效的设备可以升级"
        
        # 验证当前服务器状态
        iot_client = IoTClientManager.get_instance()
        if not iot_client.is_connected():
            return False, "IoT服务未启动，无法创建OTA任务。"
        
        # 创建OTA任务
        created_tasks = []
        for device_id in valid_devices:
            task = _create_ota_task(device_id, firmware_path, firmware_version)
            if task:
                created_tasks.append(task)
        
        if not created_tasks:
            return False, "创建OTA任务失败"
        
        # 提交任务到并行管理器
        submitted_count = 0
        for task in created_tasks:
            if _submit_task_to_manager(task.id):
                submitted_count += 1
        
        message = (f"成功创建 {len(created_tasks)} 个OTA任务，"
                  f"已提交 {submitted_count} 个任务到执行队列")
        
        logger.info(message)
        return True, message
        
    except Exception as e:
        logger.error(f"启动并行OTA任务失败: {e}")
        return False, f"启动任务失败: {str(e)}"


def retry_ota_task(task_id: int) -> Tuple[bool, str]:
    """重试OTA任务
    
    Args:
        task_id: 任务ID
    
    Returns:
        (success, message): 成功状态和消息
    """
    try:
        # 获取任务信息
        task = OtaTask.query.get_or_404(task_id)
        
        # 重置任务状态
        task.status = "等待中"
        task.progress = 0
        task.error_message = ""
        task.updated_at = datetime.now()
        db.session.commit()
        
        # 提交到并行管理器
        if _submit_task_to_manager(task_id):
            return True, "任务已重新开始"
        else:
            return False, "提交任务到执行队列失败"
            
    except Exception as e:
        db.session.rollback()
        logger.error(f"重试任务失败: {e}")
        return False, str(e)


def pause_ota_task(task_id: int) -> Tuple[bool, str]:
    """暂停OTA任务
    
    Args:
        task_id: 任务ID
    
    Returns:
        (success, message): 成功状态和消息
    """
    try:
        # 调用并行管理器的暂停功能
        success = parallel_ota_manager.pause_task(task_id)
        
        if success:
            return True, f"任务 {task_id} 暂停请求已发送"
        else:
            return False, f"任务 {task_id} 暂停失败"
            
    except Exception as e:
        logger.error(f"暂停任务失败: {e}")
        return False, str(e)


def cancel_ota_task(task_id: int) -> Tuple[bool, str]:
    """取消OTA任务
    
    Args:
        task_id: 任务ID
    
    Returns:
        (success, message): 成功状态和消息
    """
    try:
        # 调用并行管理器的取消功能
        success = parallel_ota_manager.cancel_task(task_id)
        
        if success:
            return True, f"任务 {task_id} 已取消"
        else:
            return False, f"任务 {task_id} 取消失败"
            
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        return False, str(e)


def get_ota_task_status(task_id: int) -> Optional[dict]:
    """获取OTA任务详细状态
    
    Args:
        task_id: 任务ID
    
    Returns:
        任务状态字典或None
    """
    return parallel_ota_manager.get_task_status(task_id)


def get_ota_service_stats() -> dict:
    """获取OTA服务统计信息"""
    return parallel_ota_manager.get_stats()


def _process_firmware_info(firmware_id: int = None, firmware_file=None) -> Optional[Tuple[str, str]]:
    """处理固件信息（重构版）"""
    try:
        if firmware_id:
            return _process_database_firmware(firmware_id)
        elif firmware_file:
            return _process_uploaded_firmware(firmware_file)
        else:
            logger.error("必须提供固件ID或固件文件")
            return None

    except Exception as e:
        logger.error(f"处理固件信息失败: {e}")
        return None


def _process_database_firmware(firmware_id: int) -> Optional[Tuple[str, str]]:
    """处理数据库中的固件"""
    firmware = Firmware.query.get(firmware_id)
    if not firmware:
        logger.error(f"固件 {firmware_id} 不存在")
        return None

    firmware_path = firmware.file_path
    firmware_version = firmware.version

    # 验证固件文件
    is_valid, error_msg = FirmwareValidator.validate_firmware_file(firmware_path)
    if not is_valid:
        logger.error(f"固件文件验证失败: {error_msg}")
        return None

    return firmware_path, firmware_version


def _process_uploaded_firmware(firmware_file) -> Optional[Tuple[str, str]]:
    """处理上传的固件文件"""
    from flask import current_app

    upload_folder = current_app.config.get('FIRMWARE_UPLOAD_FOLDER', 'uploads/firmware')
    allowed_extensions = ['.bin', '.hex', '.fw', '.img']

    # 保存上传的文件
    success, firmware_path, error_msg = FileManager.save_uploaded_file(
        firmware_file, upload_folder, allowed_extensions
    )

    if not success:
        logger.error(f"保存固件文件失败: {error_msg}")
        return None

    # 从文件名提取版本信息
    firmware_version = FirmwareValidator.extract_version_from_filename(
        firmware_file.filename
    )

    # 验证固件文件
    is_valid, error_msg = FirmwareValidator.validate_firmware_file(firmware_path)
    if not is_valid:
        logger.error(f"固件文件验证失败: {error_msg}")
        # 删除无效文件
        try:
            os.remove(firmware_path)
        except:
            pass
        return None

    return firmware_path, firmware_version


def _validate_devices(device_ids: List[int], firmware_version: str) -> List[int]:
    """验证设备列表（重构版）"""
    # 基本验证
    valid_devices, error_messages = DeviceValidator.validate_device_list(device_ids)

    if error_messages:
        for error in error_messages:
            logger.warning(error)

    # 兼容性检查
    compatible_devices = []
    for device_id in valid_devices:
        is_compatible, message = DeviceValidator.check_device_compatibility(
            device_id, firmware_version
        )

        if is_compatible:
            compatible_devices.append(device_id)
            logger.info(message)
        else:
            logger.info(f"跳过设备 {device_id}: {message}")

    return compatible_devices


def _create_ota_task(device_id: int, firmware_path: str, firmware_version: str) -> Optional[OtaTask]:
    """创建OTA任务（兼容旧数据库结构）"""
    try:
        # 基本任务数据
        task_data = {
            'device_id': device_id,
            'firmware_path': firmware_path,
            'firmware_version': firmware_version,
            'status': "等待中",
            'progress': 0,
        }

        # 安全地添加新字段（如果数据库支持）
        try:
            # 检查数据库是否支持新字段
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('ota_task')]

            if 'detailed_status' in columns:
                task_data['detailed_status'] = "等待中"
            if 'stage_info' in columns:
                task_data['stage_info'] = "任务已创建，等待执行"
            if 'retry_count' in columns:
                task_data['retry_count'] = 0
            if 'max_retries' in columns:
                task_data['max_retries'] = 3

        except Exception as e:
            logger.debug(f"检查新字段时出错，使用基本字段: {e}")

        # 创建任务对象
        task = OtaTask(**task_data)
        db.session.add(task)
        db.session.commit()

        logger.info(f"创建OTA任务成功: 任务ID={task.id}, 设备ID={device_id}")
        return task

    except Exception as e:
        db.session.rollback()
        logger.error(f"创建OTA任务失败: {e}")
        return None


def _submit_task_to_manager(task_id: int) -> bool:
    """提交任务到并行管理器"""
    try:
        # 创建任务执行函数
        def execute_ota_task():
            state_manager = parallel_ota_manager.task_states.get(task_id)
            if not state_manager:
                logger.error(f"任务 {task_id} 状态管理器不存在")
                return False
            
            executor = create_ota_task_executor(task_id, state_manager)
            return executor.execute()
        
        # 提交任务
        return parallel_ota_manager.submit_task(
            task_id=task_id,
            task_func=execute_ota_task,
            priority=5  # 默认优先级
        )
        
    except Exception as e:
        logger.error(f"提交任务到管理器失败: {e}")
        return False
