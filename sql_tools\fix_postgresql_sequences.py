#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复PostgreSQL序列问题
在数据迁移后，需要重置SERIAL字段的序列值
"""
import psycopg2
from psycopg2 import sql

# PostgreSQL数据库配置
POSTGRESQL_CONFIGS = {
    'production': {
        'host': '**************',
        'port': 5432,
        'database': 'kafangcharging',
        'user': 'kafanglinlin',
        'password': '7jbWNHYZZLMa',
        'schema': 'kafanglinlin_schema'
    },
    'debug': {
        'host': '**************',
        'port': 5432,
        'database': 'kfchargingdbg',
        'user': 'KfChargingDbgC',
        'password': 'JT5WJ6Zn3hbAcWBz',
        'schema': 'kfchargingdbgc_schema'
    }
}

# 需要重置序列的表和字段
TABLES_WITH_SEQUENCES = [
    'users',
    'device',
    'firmware',
    'login_logs',
    'device_parameter',
    'device_locations',
    'ota_task',
    'paid_downloads',
    'download_orders',
    'merchants',
    'debug_script'
]

def fix_sequences(config, db_name):
    """修复序列问题"""
    try:
        print(f"\n正在修复 {db_name} 数据库的序列...")
        
        # 建立连接
        conn = psycopg2.connect(
            host=config['host'],
            port=config['port'],
            database=config['database'],
            user=config['user'],
            password=config['password'],
            sslmode='disable'
        )
        
        cursor = conn.cursor()
        schema_name = config['schema']
        
        # 设置search_path
        cursor.execute(f"SET search_path TO {schema_name}, public;")
        
        for table_name in TABLES_WITH_SEQUENCES:
            try:
                # 获取表中的最大ID值
                cursor.execute(f"SELECT MAX(id) FROM {schema_name}.{table_name};")
                max_id = cursor.fetchone()[0]
                
                if max_id is None:
                    max_id = 0
                
                # 重置序列
                sequence_name = f"{table_name}_id_seq"
                new_value = max_id + 1
                
                cursor.execute(f"SELECT setval('{schema_name}.{sequence_name}', {new_value});")
                
                print(f"  ✓ {table_name}: 序列重置为 {new_value} (最大ID: {max_id})")
                
            except Exception as e:
                print(f"  ✗ {table_name}: 序列重置失败 - {e}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"✓ {db_name} 数据库序列修复完成")
        return True
        
    except Exception as e:
        print(f"✗ {db_name} 数据库序列修复失败: {e}")
        return False

def verify_sequences(config, db_name):
    """验证序列是否正确"""
    try:
        print(f"\n验证 {db_name} 数据库序列...")
        
        # 建立连接
        conn = psycopg2.connect(
            host=config['host'],
            port=config['port'],
            database=config['database'],
            user=config['user'],
            password=config['password'],
            sslmode='disable'
        )
        
        cursor = conn.cursor()
        schema_name = config['schema']
        
        # 设置search_path
        cursor.execute(f"SET search_path TO {schema_name}, public;")
        
        all_good = True
        
        for table_name in TABLES_WITH_SEQUENCES:
            try:
                # 获取表中的最大ID值
                cursor.execute(f"SELECT MAX(id) FROM {schema_name}.{table_name};")
                max_id = cursor.fetchone()[0]
                
                if max_id is None:
                    max_id = 0
                
                # 获取序列当前值
                sequence_name = f"{table_name}_id_seq"
                cursor.execute(f"SELECT last_value FROM {schema_name}.{sequence_name};")
                seq_value = cursor.fetchone()[0]
                
                if seq_value > max_id:
                    print(f"  ✓ {table_name}: 序列值 {seq_value} > 最大ID {max_id}")
                else:
                    print(f"  ✗ {table_name}: 序列值 {seq_value} <= 最大ID {max_id}")
                    all_good = False
                
            except Exception as e:
                print(f"  ✗ {table_name}: 验证失败 - {e}")
                all_good = False
        
        cursor.close()
        conn.close()
        
        if all_good:
            print(f"✓ {db_name} 数据库序列验证通过")
        else:
            print(f"✗ {db_name} 数据库序列验证失败")
        
        return all_good
        
    except Exception as e:
        print(f"✗ {db_name} 数据库序列验证失败: {e}")
        return False

def main():
    """主函数"""
    print("PostgreSQL序列修复工具")
    print("=" * 50)
    
    success_count = 0
    total_count = len(POSTGRESQL_CONFIGS)
    
    for db_name, config in POSTGRESQL_CONFIGS.items():
        print(f"\n{'='*20} 处理 {db_name} 数据库 {'='*20}")
        
        # 修复序列
        if fix_sequences(config, db_name):
            # 验证序列
            if verify_sequences(config, db_name):
                success_count += 1
                print(f"✓ {db_name} 数据库处理成功")
            else:
                print(f"✗ {db_name} 数据库验证失败")
        else:
            print(f"✗ {db_name} 数据库修复失败")
    
    print(f"\n{'='*50}")
    print(f"处理结果: {success_count}/{total_count} 个数据库成功")
    
    if success_count == total_count:
        print("🎉 所有数据库序列修复成功！")
        return True
    else:
        print("❌ 部分数据库序列修复失败")
        return False

if __name__ == '__main__':
    success = main()
    if success:
        print("\n✅ 序列修复完成，现在可以正常插入新记录了")
    else:
        print("\n❌ 序列修复失败，请检查错误信息")
