<!-- OTA升级模态框 -->
<div class="modal fade" id="otaModal" tabindex="-1" aria-labelledby="otaModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="otaModalLabel">
                    <i class="fas fa-sync-alt text-primary me-2"></i>固件升级
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="otaForm">
                <div class="modal-body">
                    <input type="hidden" id="otaDeviceId" name="device_ids[]">
                    <div class="mb-3">
                        <label for="firmwareSelect" class="form-label">选择固件</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-file-code"></i></span>
                            <select class="form-select" id="firmwareSelect" name="firmware_id" required>
                                <option value="">请选择固件...</option>
                                {% for firmware in firmwares %}
                                <option value="{{ firmware.id }}" data-version="{{ firmware.version }}">
                                    {{ firmware.name }} (v{{ firmware.version }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-text">从已上传的固件中选择需要升级的版本</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>开始升级
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- OTA相关的JavaScript代码 -->
<script>
function startOta(deviceId) {
    // 设置设备ID
    document.getElementById('otaDeviceId').value = deviceId;
    // 显示模态框
    var otaModal = new bootstrap.Modal(document.getElementById('otaModal'));
    otaModal.show();
}

// 处理OTA表单提交
document.getElementById('otaForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const deviceId = document.getElementById('otaDeviceId').value;
    const firmwareSelect = document.getElementById('firmwareSelect');
    const firmwareId = firmwareSelect.value;
    
    if (!firmwareId) {
        alert('请选择要升级的固件');
        return;
    }
    
    try {
        const response = await fetch('/ota/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                device_ids: [deviceId],
                firmware_id: firmwareId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('otaModal'));
            modal.hide();

            // 显示成功消息
            showNotification('OTA升级任务已创建', 'success');

            // 可选：刷新当前页面的设备列表（如果已实现分页）
            if (typeof refreshDeviceList === 'function') {
                refreshDeviceList();
            }
        } else {
            showNotification(data.message || '创建OTA任务失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('创建OTA任务失败，请重试', 'error');
    }
});

// 通知函数
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}
</script> 