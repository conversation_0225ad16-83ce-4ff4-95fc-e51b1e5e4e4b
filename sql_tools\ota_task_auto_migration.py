#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA任务表自动迁移脚本
为ota_task表添加并行OTA升级所需的新字段
"""

import psycopg2
import sys
import os
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'ota_migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# PostgreSQL数据库配置
POSTGRESQL_CONFIGS = {
    'debug': {
        'host': '**************',
        'port': 5432,
        'database': 'kfchargingdbg',
        'user': 'KfChargingDbgC',
        'password': 'JT5WJ6Zn3hbAcWBz',
        'schema': 'kfchargingdbgc_schema'
    },
    'production': {
        'host': '**************',
        'port': 5432,
        'database': 'kafangcharging',
        'user': 'kafanglinlin',
        'password': '7jbWNHYZZLMa',
        'schema': 'kafanglinlin_schema'
    }
}

# 新字段定义
NEW_COLUMNS = [
    {
        'name': 'detailed_status',
        'type': 'VARCHAR(50)',
        'default': "'等待中'",
        'comment': '详细状态：等待中、初始化中、连接设备中等'
    },
    {
        'name': 'stage_info',
        'type': 'TEXT',
        'default': "''",
        'comment': '当前阶段的详细信息'
    },
    {
        'name': 'retry_count',
        'type': 'INTEGER',
        'default': '0',
        'comment': '当前重试次数'
    },
    {
        'name': 'max_retries',
        'type': 'INTEGER',
        'default': '3',
        'comment': '最大允许重试次数'
    },
    {
        'name': 'started_at',
        'type': 'TIMESTAMP',
        'default': 'NULL',
        'comment': '任务开始执行时间'
    },
    {
        'name': 'completed_at',
        'type': 'TIMESTAMP',
        'default': 'NULL',
        'comment': '任务完成时间（成功或失败）'
    }
]

# 新索引定义
NEW_INDEXES = [
    {
        'name': 'idx_ota_task_detailed_status',
        'columns': ['detailed_status']
    },
    {
        'name': 'idx_ota_task_retry_count',
        'columns': ['retry_count']
    },
    {
        'name': 'idx_ota_task_started_at',
        'columns': ['started_at']
    }
]


class OtaTaskMigrator:
    """OTA任务表迁移器"""
    
    def __init__(self, config):
        self.config = config
        self.conn = None
        self.cursor = None
        self.schema = config['schema']
    
    def connect(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password'],
                sslmode='disable'
            )
            self.cursor = self.conn.cursor()
            
            # 设置搜索路径
            self.cursor.execute(f"SET search_path TO {self.schema}, public;")
            self.conn.commit()
            
            logger.info(f"成功连接到数据库: {self.config['database']}, schema: {self.schema}")
            return True
            
        except Exception as e:
            logger.error(f"连接数据库失败: {e}")
            return False
    
    def check_table_exists(self):
        """检查ota_task表是否存在"""
        try:
            self.cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = 'ota_task'
                )
            """, (self.schema,))
            
            exists = self.cursor.fetchone()[0]
            if exists:
                logger.info("ota_task表存在")
                return True
            else:
                logger.error("ota_task表不存在")
                return False
                
        except Exception as e:
            logger.error(f"检查表存在性失败: {e}")
            return False
    
    def get_existing_columns(self):
        """获取现有列信息"""
        try:
            self.cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default 
                FROM information_schema.columns 
                WHERE table_schema = %s AND table_name = 'ota_task' 
                ORDER BY ordinal_position
            """, (self.schema,))
            
            columns = self.cursor.fetchall()
            logger.info(f"现有列数量: {len(columns)}")
            
            return {col[0]: {'type': col[1], 'nullable': col[2], 'default': col[3]} 
                    for col in columns}
            
        except Exception as e:
            logger.error(f"获取现有列信息失败: {e}")
            return {}
    
    def column_exists(self, column_name):
        """检查列是否存在"""
        try:
            self.cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.columns 
                    WHERE table_schema = %s AND table_name = 'ota_task' AND column_name = %s
                )
            """, (self.schema, column_name))
            
            return self.cursor.fetchone()[0]
            
        except Exception as e:
            logger.error(f"检查列存在性失败: {e}")
            return False
    
    def add_column(self, column_info):
        """添加新列"""
        try:
            column_name = column_info['name']
            column_type = column_info['type']
            default_value = column_info['default']
            comment = column_info['comment']
            
            if self.column_exists(column_name):
                logger.info(f"列 {column_name} 已存在，跳过")
                return True
            
            # 添加列
            if default_value == 'NULL':
                sql = f"ALTER TABLE {self.schema}.ota_task ADD COLUMN {column_name} {column_type}"
            else:
                sql = f"ALTER TABLE {self.schema}.ota_task ADD COLUMN {column_name} {column_type} DEFAULT {default_value}"
            
            logger.info(f"执行SQL: {sql}")
            self.cursor.execute(sql)
            
            # 添加注释
            comment_sql = f"COMMENT ON COLUMN {self.schema}.ota_task.{column_name} IS '{comment}'"
            self.cursor.execute(comment_sql)
            
            self.conn.commit()
            logger.info(f"成功添加列: {column_name}")
            return True
            
        except Exception as e:
            logger.error(f"添加列 {column_name} 失败: {e}")
            self.conn.rollback()
            return False
    
    def create_index(self, index_info):
        """创建索引"""
        try:
            index_name = index_info['name']
            columns = index_info['columns']
            
            # 检查索引是否已存在
            self.cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_indexes 
                    WHERE schemaname = %s AND tablename = 'ota_task' AND indexname = %s
                )
            """, (self.schema, index_name))
            
            if self.cursor.fetchone()[0]:
                logger.info(f"索引 {index_name} 已存在，跳过")
                return True
            
            # 创建索引
            columns_str = ', '.join(columns)
            sql = f"CREATE INDEX {index_name} ON {self.schema}.ota_task({columns_str})"
            
            logger.info(f"执行SQL: {sql}")
            self.cursor.execute(sql)
            self.conn.commit()
            
            logger.info(f"成功创建索引: {index_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建索引 {index_name} 失败: {e}")
            self.conn.rollback()
            return False
    
    def update_existing_data(self):
        """更新现有数据"""
        try:
            logger.info("开始更新现有数据...")
            
            # 更新detailed_status字段，使其与status字段保持一致
            update_sql = f"""
                UPDATE {self.schema}.ota_task 
                SET detailed_status = status 
                WHERE detailed_status = '等待中' AND status != '等待中'
            """
            self.cursor.execute(update_sql)
            updated_rows = self.cursor.rowcount
            logger.info(f"更新了 {updated_rows} 行的detailed_status字段")
            
            # 为已完成的任务设置完成时间
            complete_sql = f"""
                UPDATE {self.schema}.ota_task 
                SET completed_at = updated_at 
                WHERE status IN ('成功', '失败') AND completed_at IS NULL
            """
            self.cursor.execute(complete_sql)
            completed_rows = self.cursor.rowcount
            logger.info(f"为 {completed_rows} 行设置了完成时间")
            
            self.conn.commit()
            logger.info("现有数据更新完成")
            return True
            
        except Exception as e:
            logger.error(f"更新现有数据失败: {e}")
            self.conn.rollback()
            return False
    
    def verify_migration(self):
        """验证迁移结果"""
        try:
            logger.info("开始验证迁移结果...")
            
            # 检查所有新字段是否存在
            missing_columns = []
            for column_info in NEW_COLUMNS:
                if not self.column_exists(column_info['name']):
                    missing_columns.append(column_info['name'])
            
            if missing_columns:
                logger.error(f"以下字段缺失: {', '.join(missing_columns)}")
                return False
            
            # 统计数据
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN detailed_status IS NOT NULL THEN 1 END) as with_detailed_status,
                    COUNT(CASE WHEN stage_info IS NOT NULL THEN 1 END) as with_stage_info,
                    COUNT(CASE WHEN retry_count IS NOT NULL THEN 1 END) as with_retry_count,
                    COUNT(CASE WHEN max_retries IS NOT NULL THEN 1 END) as with_max_retries,
                    COUNT(CASE WHEN started_at IS NOT NULL THEN 1 END) as with_started_at,
                    COUNT(CASE WHEN completed_at IS NOT NULL THEN 1 END) as with_completed_at
                FROM {self.schema}.ota_task
            """)
            
            stats = self.cursor.fetchone()
            logger.info(f"验证结果:")
            logger.info(f"  总记录数: {stats[0]}")
            logger.info(f"  有detailed_status的记录: {stats[1]}")
            logger.info(f"  有stage_info的记录: {stats[2]}")
            logger.info(f"  有retry_count的记录: {stats[3]}")
            logger.info(f"  有max_retries的记录: {stats[4]}")
            logger.info(f"  有started_at的记录: {stats[5]}")
            logger.info(f"  有completed_at的记录: {stats[6]}")
            
            logger.info("✓ 迁移验证成功")
            return True
            
        except Exception as e:
            logger.error(f"验证迁移结果失败: {e}")
            return False
    
    def migrate(self):
        """执行完整迁移"""
        try:
            logger.info(f"开始迁移数据库: {self.config['database']}")
            
            # 检查表是否存在
            if not self.check_table_exists():
                return False
            
            # 显示现有列信息
            existing_columns = self.get_existing_columns()
            logger.info(f"现有列: {list(existing_columns.keys())}")
            
            # 添加新列
            success_count = 0
            for column_info in NEW_COLUMNS:
                if self.add_column(column_info):
                    success_count += 1
            
            logger.info(f"成功添加 {success_count}/{len(NEW_COLUMNS)} 个新列")
            
            # 创建索引
            index_success_count = 0
            for index_info in NEW_INDEXES:
                if self.create_index(index_info):
                    index_success_count += 1
            
            logger.info(f"成功创建 {index_success_count}/{len(NEW_INDEXES)} 个新索引")
            
            # 更新现有数据
            if not self.update_existing_data():
                logger.warning("更新现有数据失败，但迁移继续")
            
            # 验证迁移结果
            if not self.verify_migration():
                logger.error("迁移验证失败")
                return False
            
            logger.info(f"✓ 数据库 {self.config['database']} 迁移成功")
            return True
            
        except Exception as e:
            logger.error(f"迁移过程中发生错误: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("数据库连接已关闭")


def main():
    """主函数"""
    logger.info("开始OTA任务表自动迁移")
    logger.info("=" * 60)
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        env = sys.argv[1]
        if env not in POSTGRESQL_CONFIGS:
            logger.error(f"无效的环境参数: {env}，支持的环境: {list(POSTGRESQL_CONFIGS.keys())}")
            return False
        environments = [env]
    else:
        # 默认只迁移测试环境
        environments = ['debug']
        logger.info("未指定环境，默认只迁移测试环境")
    
    success_count = 0
    total_count = len(environments)
    
    for env in environments:
        logger.info(f"\n{'='*20} 迁移 {env} 环境 {'='*20}")
        
        config = POSTGRESQL_CONFIGS[env]
        migrator = OtaTaskMigrator(config)
        
        try:
            if migrator.connect():
                if migrator.migrate():
                    success_count += 1
                    logger.info(f"✓ {env} 环境迁移成功")
                else:
                    logger.error(f"✗ {env} 环境迁移失败")
            else:
                logger.error(f"✗ {env} 环境连接失败")
        
        except Exception as e:
            logger.error(f"✗ {env} 环境迁移异常: {e}")
        
        finally:
            migrator.close()
    
    # 总结结果
    logger.info(f"\n{'='*60}")
    logger.info(f"迁移完成: {success_count}/{total_count} 个环境迁移成功")
    
    if success_count == total_count:
        logger.info("✓ 所有环境迁移成功！")
        return True
    else:
        logger.error("✗ 部分环境迁移失败")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
