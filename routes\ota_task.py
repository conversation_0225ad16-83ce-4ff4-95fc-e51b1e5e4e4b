from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.database import db
from models.ota_task import OtaTask

# 创建蓝图
ota_task_bp = Blueprint('ota_task', __name__)

@ota_task_bp.route('/ota/task/<int:task_id>')
@login_required
def get_task_status(task_id):
    """获取任务状态"""
    task = OtaTask.query.get_or_404(task_id)
    return jsonify({
        'id': task.id,
        'device_id': task.device_id,
        'device_name': task.device.device_id,
        'firmware_version': task.firmware_version,
        'status': task.status,
        'progress': task.progress,
        'error_message': task.error_message,
        'created_at': task.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        'updated_at': task.updated_at.strftime('%Y-%m-%d %H:%M:%S')
    })
