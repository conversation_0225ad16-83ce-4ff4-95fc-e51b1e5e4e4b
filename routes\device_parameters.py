from flask import Blueprint
from flask_login import login_required
from models.device import Device
from models.device_parameter import DeviceParameter
from utils.logger import setup_logging
from services.iot_client_manager import IoTClientManager
from iot_client.functions.register_manager import RegisterManager
from iot_client.bin_block.reg_addr import RegAddr
from services.device_location import update_device_location_db
from flask import render_template, jsonify, request, flash, redirect, url_for
from models.database import db

# 获取日志记录器
logger = setup_logging()

device_parameters_bp = Blueprint('device_parameters', __name__)

@device_parameters_bp.route('/device/<int:id>/parameters', methods=['GET'])
@login_required
def device_parameters(id):
    """设备参数页面"""
    device = Device.query.get_or_404(id)
    try:
        # 从数据库中获取设备参数，如果不存在则返回空列表
        parameters = DeviceParameter.query.filter_by(device_id=device.id).all()
    except Exception as e:
        logger.error(f"获取设备参数异常: {e}")

    return render_template('device_parameters.html', device=device, parameters=parameters)

@device_parameters_bp.route('/api/device/<device_id>/location', methods=['GET'])
def get_device_location(device_id):
    """获取设备位置"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 读取所有寄存器
        result = {}

        # 读取位置信息寄存器 REG_LOCATION_CODE - REG_LOCATION_LONGITUDE_L
        def read_location_info(device_id:str):
            msg = reg_manager.read_registers(int(device_id), RegAddr.REG_LOCATION_CODE, 5)
            if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
                values = msg['parsed_data']['register_value']
                if len(values) == 5:
                    location_code = values[0]
                    latitude = ((values[1] & 0xFFFF) << 16 | values[2]) * 1e-7
                    longitude = ((values[3] & 0xFFFF) << 16 | values[4]) * 1e-7
                    logger.info(f"位置信息: 位置代码: {location_code}, 纬度: {latitude}, 经度: {longitude}")
                    # 更新数据库
                    update_device_location_db(device_id, latitude, longitude, "")
                    # 返回位置信息
                    return {
                        'location_code': location_code,
                        'latitude': latitude,
                        'longitude': longitude
                    }
            return None

        # 读取位置信息
        location_info = read_location_info(device.device_id)

        if location_info:
            return jsonify({
                'success': True,
                'location': location_info
            })
        else:
            return jsonify({'error': '无法获取设备位置信息'}), 500

    except Exception as e:
        logger.error(f"获取设备位置异常: {e}")
        return jsonify({'error': str(e)}), 500

# 定义处理寄存器值的函数
def process_register_value(reg_addr, value, result):
    """处理寄存器值并添加到结果字典中"""
    match reg_addr:
        case RegAddr.REG_VERSION_H:
            pcb_version = (value >> 8) & 0xFF
            pcb_version = "新版BL0910" if pcb_version >= 50 else "旧版霍尔"
            fw_version = value & 0xFF
            result[RegAddr.get_reg_name(reg_addr)] = f"PCB:{pcb_version},FW Major:{fw_version}"
        case RegAddr.REG_VERSION_L:
            fw_minor = (value >> 8) & 0xFF
            fw_patch = value & 0xFF
            result[RegAddr.get_reg_name(reg_addr)] = f"FW Minor.Patch:{fw_minor}.{fw_patch}"
        case RegAddr.REG_CTRL1:
            # 控制寄存器，bit0: 控制SIM卡拔出功能，bit1: 控制LED闪烁模式。
            sim_status = (value >> 0) & 0x01
            led_status = (value >> 1) & 0x01
            sim_status = "SIM卡拔出启动所有口" if sim_status == 0 else "SIM卡拔出无操做"
            led_status = "LED闪烁" if led_status == 1 else "LED呼吸灯"
            result[RegAddr.get_reg_name(reg_addr)] = f"{value},SIM:{sim_status},LED:{led_status}"
        case RegAddr.REG_CSQ:
            csq = (value >> 8) & 0xFF
            ber = value & 0xFF
            result[RegAddr.get_reg_name(reg_addr)] = f"CSQ: {csq}, BER: {ber}"
        case RegAddr.REG_PERSENTAGE:
            persentage = value * 0.01
            result[RegAddr.get_reg_name(reg_addr)] = f"{persentage}%"
        case _:
            result[RegAddr.get_reg_name(reg_addr)] = value

def update_device_version_from_params(device: Device) -> bool:
    """
    根据设备参数表中的版本号寄存器更新设备版本号
    
    Args:
        device: 设备对象
        
    Returns:
        bool: 更新是否成功
    """
    try:
        # 从已保存的参数中获取版本号信息
        version_h_param = DeviceParameter.query.filter_by(device_id=device.id, param_name='REG_VERSION_H').first()
        version_l_param = DeviceParameter.query.filter_by(device_id=device.id, param_name='REG_VERSION_L').first()
        
        if version_h_param and version_l_param:
            # 解析版本号高字节
            version_h = int(version_h_param.param_value.split(',')[0])  # 获取数值部分
            fw_major = version_h & 0xFF
            
            # 解析版本号低字节
            version_l = int(version_l_param.param_value.split('.')[0].split(':')[1])  # 获取数值部分
            fw_minor = (version_l >> 8) & 0xFF
            fw_patch = version_l & 0xFF
            
            # 构建完整版本号字符串
            new_version = f"{fw_major}.{fw_minor}.{fw_patch}"
            
            # 比较版本号，只有在版本号不同时才更新
            if not device.firmware_version or new_version != device.firmware_version:
                device.firmware_version = new_version
                db.session.commit()
                logger.info(f"设备 {device.device_id} 版本号已更新为: {new_version}")
                return True
            else:
                logger.debug(f"设备 {device.device_id} 版本号未变化: {new_version}")
                return True
    except Exception as e:
        logger.error(f"更新设备版本号异常: {e}")
    return False

@device_parameters_bp.route('/api/device/<device_id>/parameters', methods=['GET'])
def get_device_parameters(device_id):
    """获取设备参数"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 读取所有寄存器
        result = {}
        
        # 读取寄存器并处理数据的函数
        def read_and_process_registers(device_id, start_addr, count):
            msg = reg_manager.read_registers(int(device_id), start_addr, count)
            if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
                # 修正字段名称，将 parsed_data 改为 parsed_data
                if 'parsed_data' in msg:
                    msg['parsed_data'] = msg.pop('parsed_data')
                values = msg['parsed_data']['register_value']
                for i, value in enumerate(values):
                    reg_addr = start_addr + i
                    process_register_value(reg_addr, value, result)

        # 读取T1-T13寄存器
        read_and_process_registers(device.device_id, RegAddr.REG_T1, 13)

        # 读取P4-P16寄存器
        read_and_process_registers(device.device_id, RegAddr.REG_P4, 13)

        # 构建返回结果
        response = {}
        for reg_name, value in result.items():
            response[reg_name] = {
                'value': value,
                'description': RegAddr.get_reg_desc(reg_name)
            }

        # 将获取到的参数保存到数据库
        for reg_name, param_data in response.items():
            param_value = str(param_data['value'])
            description = param_data['description']
            # 检查是否已存在该参数记录
            existing_param = DeviceParameter.query.filter_by(device_id=device.id, param_name=reg_name).first()
            if existing_param:
                # 更新现有记录
                existing_param.param_value = param_value
                existing_param.description = description
            else:
                # 创建新记录
                new_param = DeviceParameter(
                    device_id=device.id,
                    param_name=reg_name,
                    param_value=param_value,
                    description=description
                )
                db.session.add(new_param)

        # 提交数据库更改
        db.session.commit()
        logger.info(f"设备 {device.device_id} 的参数已保存到数据库")

        # 根具版本号寄存器来更新数据库中记录的版本号
        update_device_version_from_params(device)

        return jsonify(response)

    except Exception as e:
        logger.error(f"获取设备参数异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/api/device/<device_id>/parameters', methods=['POST'])
def set_device_parameters(device_id):
    """设置设备参数"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        data = request.get_json()
        if not data or 'reg_addr' not in data or 'reg_value' not in data:
            return jsonify({'error': '参数不完整'}), 400

        reg_addr = int(data['reg_addr'])
        reg_value = int(data['reg_value'])

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 写入寄存器
        success = reg_manager.write_register(int(device.device_id), reg_addr, [reg_value])

        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': '写入寄存器失败'}), 500

    except Exception as e:
        logger.error(f"设置设备参数异常: {e}")
        return jsonify({'error': str(e)}), 500


@device_parameters_bp.route('/api/device/<device_id>/saved_parameters', methods=['GET'])
@login_required
def get_saved_device_parameters(device_id):
    """从数据库中获取指定设备的参数"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 从数据库中获取设备参数
        parameters = DeviceParameter.query.filter_by(device_id=device.id).all()

        # 构建返回结果
        response = {}
        for param in parameters:
            response[param.param_name] = {
                'value': param.param_value,
                'description': param.description
            }

        return jsonify(response)

    except Exception as e:
        logger.error(f"获取设备参数异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/parameter/add/<int:device_id>', methods=['POST'])
@login_required
def add_parameter(device_id):
    """添加设备参数"""
    device = Device.query.get_or_404(device_id)

    param_name = request.form.get('param_name')
    param_value = request.form.get('param_value')

    if not param_name or not param_value:
        flash('参数名称和值不能为空', 'danger')
        return redirect(url_for('device_parameters.device_parameters', id=device_id))

    try:
        # 这里可以添加参数到数据库或设备
        # 暂时只显示成功消息
        flash('参数添加成功', 'success')
    except Exception as e:
        logger.error(f"添加参数异常: {e}")
        flash(f'添加参数失败: {str(e)}', 'danger')

    return redirect(url_for('device_parameters.device_parameters', id=device_id))

@device_parameters_bp.route('/parameter/edit/<int:id>', methods=['POST'])
@login_required
def edit_parameter(id):
    """编辑设备参数"""
    # 这里应该从数据库获取参数
    # 暂时使用模拟数据
    param_name = request.form.get('param_name')
    param_value = request.form.get('param_value')

    if not param_name or not param_value:
        flash('参数名称和值不能为空', 'danger')
        return redirect(url_for('device_parameters', id=1))  # 假设设备ID为1

    try:
        # 这里可以更新参数到数据库或设备
        # 暂时只显示成功消息
        flash('参数更新成功', 'success')
    except Exception as e:
        logger.error(f"更新参数异常: {e}")
        flash(f'更新参数失败: {str(e)}', 'danger')

    return redirect(url_for('device_parameters.device_parameters', id=1))  # 假设设备ID为1

@device_parameters_bp.route('/parameter/delete/<int:id>', methods=['GET'])
@login_required
def delete_parameter(id):
    """删除设备参数"""
    try:
        # 这里可以从数据库或设备中删除参数
        # 暂时只显示成功消息
        flash('参数删除成功', 'success')
    except Exception as e:
        logger.error(f"删除参数异常: {e}")
        flash(f'删除参数失败: {str(e)}', 'danger')

    return redirect(url_for('device_parameters.device_parameters', id=1))  # 假设设备ID为1

@device_parameters_bp.route('/api/device/<device_id>/error_counts', methods=['GET'])
def get_error_counts(device_id):
    """获取设备错误计数"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 一次性读取4个连续的寄存器
        msg = reg_manager.read_registers(int(device.device_id), RegAddr.REG_ERROR_CNT1, 4)
        if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
            # 修正字段名称，将 parsed_data 改为 parsed_data
            if 'parsed_data' in msg:
                msg['parsed_data'] = msg.pop('parsed_data')
            values = msg['parsed_data']['register_value']
            if len(values) == 4:
                result = {
                    'REG_ERROR_CNT1': values[0],
                    'REG_ERROR_CNT2': values[1],
                    'REG_ERROR_CNT3': values[2],
                    'REG_ERROR_CNT4': values[3]
                }
                return jsonify(result)

        # 如果读取失败，返回默认值
        return jsonify({
            'REG_ERROR_CNT1': 0,
            'REG_ERROR_CNT2': 0,
            'REG_ERROR_CNT3': 0,
            'REG_ERROR_CNT4': 0
        })

    except Exception as e:
        logger.error(f"获取错误计数异常: {e}")
        return jsonify({'error': str(e)}), 500


@device_parameters_bp.route('/api/device/<device_id>/debug_info', methods=['GET'])
def get_device_debug_info(device_id):
    """获取设备调试信息"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 查询设备调试信息，返回响应消息
        msg = reg_manager.cmd_request_debug_info_query(int(device.device_id), 0)
        if msg:
            # 检查是否有命令响应信息
            parsed_data = msg.get('parsed_data', {})
            # 提取需要的信息
            session_id = parsed_data.get('session_id', 0)
            result = parsed_data.get('result', 1)  # 默认为错误
            info = parsed_data.get('info', {})

            # 构建简化的响应
            response = {
                'session_id': session_id,
                'result': result,
                'info': info
            }
            return jsonify(response)
        else:
            return jsonify({'error': '获取设备调试信息失败'}), 500
    except Exception as e:
        logger.error(f"获取设备调试信息异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/api/devices/batch_parameters', methods=['POST'])
@login_required
def batch_set_device_parameters():
    """批量设置设备参数"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data or 'reg_addr' not in data or 'reg_value' not in data:
            return jsonify({'error': '参数不完整'}), 400

        device_ids = data['device_ids']
        reg_addr = int(data['reg_addr'])
        reg_value = int(data['reg_value'])

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        results = []
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '设备不存在'
                    })
                    continue

                # 构建topic
                topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

                # 创建寄存器管理器
                reg_manager = RegisterManager(iot_client, topic_full_name, logger)

                # 写入寄存器
                success = reg_manager.write_register(int(device.device_id), reg_addr, [reg_value])

                results.append({
                    'device_id': device_id,
                    'success': success,
                    'error': None if success else '写入寄存器失败'
                })

            except Exception as e:
                logger.error(f"设置设备 {device_id} 参数异常: {e}")
                results.append({
                    'device_id': device_id,
                    'success': False,
                    'error': str(e)
                })

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        logger.error(f"批量设置设备参数异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/api/devices/batch_query_parameters', methods=['POST'])
@login_required
def batch_query_device_parameters():
    """批量查询设备参数"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data:
            return jsonify({'error': '参数不完整'}), 400

        device_ids = data['device_ids']

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        results = []
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '设备不存在'
                    })
                    continue

                # 构建topic
                topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

                # 创建寄存器管理器
                reg_manager = RegisterManager(iot_client, topic_full_name, logger)

                # 读取所有寄存器
                result = {}

                # 读取T1-T13寄存器
                msg = reg_manager.read_registers(int(device.device_id), RegAddr.REG_T1, 13)
                if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
                    values = msg['parsed_data']['register_value']
                    for i, value in enumerate(values):
                        reg_addr = RegAddr.REG_T1 + i
                        process_register_value(reg_addr, value, result)

                # 读取P4-P16寄存器
                msg = reg_manager.read_registers(int(device.device_id), RegAddr.REG_P4, 13)
                if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
                    values = msg['parsed_data']['register_value']
                    for i, value in enumerate(values):
                        reg_addr = RegAddr.REG_P4 + i
                        process_register_value(reg_addr, value, result)

                results.append({
                    'device_id': device_id,
                    'success': True,
                    'parameters': result
                })

            except Exception as e:
                logger.error(f"查询设备 {device_id} 参数异常: {e}")
                results.append({
                    'device_id': device_id,
                    'success': False,
                    'error': str(e)
                })

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        logger.error(f"批量查询设备参数异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/api/devices/batch_query_locations', methods=['POST'])
@login_required
def batch_query_device_locations():
    """批量查询设备位置"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data:
            return jsonify({'error': '参数不完整'}), 400

        device_ids = data['device_ids']

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        results = []
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '设备不存在'
                    })
                    continue

                # 构建topic
                topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

                # 创建寄存器管理器
                reg_manager = RegisterManager(iot_client, topic_full_name, logger)

                # 读取位置信息寄存器
                msg = reg_manager.read_registers(int(device.device_id), RegAddr.REG_LOCATION_CODE, 5)
                if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
                    values = msg['parsed_data']['register_value']
                    if len(values) == 5:
                        location_code = values[0]
                        latitude = ((values[1] & 0xFFFF) << 16 | values[2]) * 1e-7
                        longitude = ((values[3] & 0xFFFF) << 16 | values[4]) * 1e-7
                        
                        # 更新数据库
                        update_device_location_db(device.device_id, latitude, longitude, "")
                        
                        results.append({
                            'device_id': device_id,
                            'success': True,
                            'location': {
                                'location_code': location_code,
                                'latitude': latitude,
                                'longitude': longitude
                            }
                        })
                    else:
                        results.append({
                            'device_id': device_id,
                            'success': False,
                            'error': '位置数据格式错误'
                        })
                else:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '无法获取位置信息'
                    })

            except Exception as e:
                logger.error(f"查询设备 {device_id} 位置异常: {e}")
                results.append({
                    'device_id': device_id,
                    'success': False,
                    'error': str(e)
                })

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        logger.error(f"批量查询设备位置异常: {e}")
        return jsonify({'error': str(e)}), 500

@device_parameters_bp.route('/api/device/<device_id>/sim_card_info', methods=['GET'])
def get_device_sim_card_info(device_id):
    """获取设备调试信息"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 检查IoT客户端是否已启动
        if not IoTClientManager.is_running():
            return jsonify({'error': 'IoT客户端未启动，请先启动客户端'}), 500

        # 使用全局IoT客户端
        iot_client = IoTClientManager.get_instance()

        # 构建topic
        topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

        # 创建寄存器管理器
        reg_manager = RegisterManager(iot_client, topic_full_name, logger)

        # 查询设备调试信息，返回响应消息
        msg = reg_manager.cmd_request_debug_info_query(int(device.device_id), 2)
        if msg:
            # 检查是否有命令响应信息
            parsed_data = msg.get('parsed_data', {})
            info = parsed_data.get('info', {})
            # 构建简化的响应
            result = {
                    'imei_len': info.get('imei_len', 0),
                    'imei': info.get('imei', ''),
                    'iccid_len': info.get('iccid_len', 0),
                    'iccid': info.get('iccid', ''),
                    'query_type': info.get('query_type', 0)
            }
            return jsonify(result)
        else:
            return jsonify({'error': '获取设备调试信息失败'}), 500
    except Exception as e:
        logger.error(f"获取设备调试信息异常: {e}")
        return jsonify({'error': str(e)}), 500