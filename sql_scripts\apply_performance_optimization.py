#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能优化脚本执行器
用于执行数据库性能优化SQL脚本
"""

import os
import sys
import psycopg2
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import Config
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        """初始化优化器"""
        self.config = Config()
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            # 使用调试环境数据库
            database_uri = self.config.DEBUG_DATABASE_URI
            logger.info(f"连接数据库: {database_uri}")
            
            # 解析数据库连接字符串
            # ****************************************************************************************************************************
            import urllib.parse
            parsed = urllib.parse.urlparse(database_uri)
            
            # 提取连接参数
            host = parsed.hostname
            port = parsed.port
            database = parsed.path[1:]  # 去掉开头的 /
            username = parsed.username
            password = parsed.password
            
            # 提取schema信息
            schema = 'kfchargingdbgc_schema'
            if parsed.query:
                query_params = urllib.parse.parse_qs(parsed.query)
                if 'options' in query_params:
                    options = query_params['options'][0]
                    if 'search_path' in options:
                        schema = options.split('search_path%3D')[1] if '%3D' in options else options.split('search_path=')[1]
            
            # 建立连接
            self.connection = psycopg2.connect(
                host=host,
                port=port,
                database=database,
                user=username,
                password=password
            )
            
            self.cursor = self.connection.cursor()
            
            # 设置schema
            self.cursor.execute(f"SET search_path TO {schema}, public;")
            self.connection.commit()
            
            logger.info(f"数据库连接成功，使用schema: {schema}")
            return True
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def execute_sql_file(self, sql_file_path):
        """执行SQL文件"""
        try:
            if not os.path.exists(sql_file_path):
                logger.error(f"SQL文件不存在: {sql_file_path}")
                return False
            
            logger.info(f"开始执行SQL文件: {sql_file_path}")
            
            # 读取SQL文件内容
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句（按分号分割）
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            success_count = 0
            error_count = 0
            
            for i, statement in enumerate(sql_statements, 1):
                try:
                    # 跳过注释行
                    if statement.startswith('--') or statement.startswith('/*'):
                        continue
                    
                    logger.info(f"执行SQL语句 {i}/{len(sql_statements)}: {statement[:100]}...")
                    
                    self.cursor.execute(statement)
                    self.connection.commit()
                    
                    success_count += 1
                    logger.info(f"SQL语句 {i} 执行成功")
                    
                except Exception as e:
                    error_count += 1
                    logger.warning(f"SQL语句 {i} 执行失败: {e}")
                    # 继续执行下一条语句
                    self.connection.rollback()
                    continue
            
            logger.info(f"SQL文件执行完成: 成功 {success_count} 条，失败 {error_count} 条")
            return error_count == 0
            
        except Exception as e:
            logger.error(f"执行SQL文件失败: {e}")
            return False
    
    def check_indexes(self):
        """检查索引创建情况"""
        try:
            logger.info("检查索引创建情况...")
            
            # 查询所有索引
            check_sql = """
            SELECT 
                schemaname,
                tablename,
                indexname,
                indexdef
            FROM pg_indexes 
            WHERE schemaname = current_schema()
            AND indexname LIKE 'idx_%'
            ORDER BY tablename, indexname;
            """
            
            self.cursor.execute(check_sql)
            indexes = self.cursor.fetchall()
            
            logger.info(f"找到 {len(indexes)} 个自定义索引:")
            
            for schema, table, index_name, index_def in indexes:
                logger.info(f"  {table}.{index_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"检查索引失败: {e}")
            return False
    
    def analyze_tables(self):
        """分析表统计信息"""
        try:
            logger.info("更新表统计信息...")
            
            # 获取当前schema中的所有表
            tables_sql = """
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = current_schema()
            AND table_type = 'BASE TABLE';
            """
            
            self.cursor.execute(tables_sql)
            tables = self.cursor.fetchall()
            
            for (table_name,) in tables:
                try:
                    analyze_sql = f"ANALYZE {table_name};"
                    self.cursor.execute(analyze_sql)
                    self.connection.commit()
                    logger.info(f"已分析表: {table_name}")
                except Exception as e:
                    logger.warning(f"分析表 {table_name} 失败: {e}")
                    self.connection.rollback()
            
            logger.info("表统计信息更新完成")
            return True
            
        except Exception as e:
            logger.error(f"分析表失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")
    
    def run_optimization(self):
        """运行性能优化"""
        try:
            logger.info("开始执行性能优化...")
            
            # 连接数据库
            if not self.connect_database():
                return False
            
            # 执行优化SQL脚本
            sql_file_path = os.path.join(os.path.dirname(__file__), 'performance_optimization.sql')
            if not self.execute_sql_file(sql_file_path):
                logger.error("执行优化SQL脚本失败")
                return False
            
            # 检查索引创建情况
            self.check_indexes()
            
            # 分析表统计信息
            self.analyze_tables()
            
            logger.info("性能优化执行完成")
            return True
            
        except Exception as e:
            logger.error(f"性能优化执行失败: {e}")
            return False
        finally:
            self.close_connection()

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("充电桩管理系统性能优化脚本")
    logger.info("=" * 50)
    
    optimizer = PerformanceOptimizer()
    
    if optimizer.run_optimization():
        logger.info("性能优化成功完成！")
        return 0
    else:
        logger.error("性能优化执行失败！")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
