#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库迁移测试脚本
验证OTA任务表迁移的正确性和安全性
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_factory import create_app
from models.database import db
from models.ota_task import OtaTask
from models.device import Device
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class DatabaseMigrationTester:
    """数据库迁移测试器"""
    
    def __init__(self):
        self.app = None
        self.test_device = None
        self.test_tasks = []
    
    def setup(self):
        """设置测试环境"""
        print("🔧 设置数据库迁移测试环境...")
        
        # 创建测试应用
        self.app = create_app('testing')
        
        with self.app.app_context():
            # 创建测试设备
            self._create_test_device()
            
        print("✅ 测试环境设置完成")
    
    def _create_test_device(self):
        """创建测试设备"""
        try:
            # 检查是否已存在测试设备
            existing_device = Device.query.filter_by(device_id="TEST_MIGRATION_DEVICE").first()
            if existing_device:
                self.test_device = existing_device
                print(f"📱 使用现有测试设备: {existing_device.id}")
                return
            
            # 创建新的测试设备
            device = Device(
                device_id="TEST_MIGRATION_DEVICE",
                device_remark="数据库迁移测试设备",
                product_key="test_product_key",
                firmware_version="1.0.0"
            )
            db.session.add(device)
            db.session.commit()
            
            self.test_device = device
            print(f"📱 创建测试设备成功: {device.id}")
            
        except Exception as e:
            print(f"❌ 创建测试设备失败: {e}")
            raise
    
    def test_basic_ota_task_creation(self):
        """测试基本OTA任务创建"""
        print("\n🧪 测试基本OTA任务创建...")
        
        with self.app.app_context():
            try:
                # 创建基本OTA任务
                task = OtaTask(
                    device_id=self.test_device.id,
                    firmware_path="/tmp/test_firmware.bin",
                    firmware_version="2.0.0",
                    status="等待中",
                    progress=0
                )
                
                db.session.add(task)
                db.session.commit()
                
                self.test_tasks.append(task)
                print(f"✅ 基本OTA任务创建成功: 任务ID={task.id}")
                
                # 验证任务数据
                retrieved_task = OtaTask.query.get(task.id)
                assert retrieved_task is not None, "任务检索失败"
                assert retrieved_task.device_id == self.test_device.id, "设备ID不匹配"
                assert retrieved_task.status == "等待中", "状态不匹配"
                
                print("✅ 基本任务数据验证通过")
                return True
                
            except Exception as e:
                print(f"❌ 基本OTA任务创建失败: {e}")
                return False
    
    def test_new_fields_compatibility(self):
        """测试新字段兼容性"""
        print("\n🧪 测试新字段兼容性...")
        
        with self.app.app_context():
            try:
                # 创建带新字段的OTA任务
                task_data = {
                    'device_id': self.test_device.id,
                    'firmware_path': "/tmp/test_firmware_v2.bin",
                    'firmware_version': "2.1.0",
                    'status': "等待中",
                    'progress': 0
                }
                
                # 尝试添加新字段
                try:
                    task_data['detailed_status'] = "等待中"
                    task_data['stage_info'] = "任务已创建"
                    task_data['retry_count'] = 0
                    task_data['max_retries'] = 3
                    print("📝 新字段数据准备完成")
                except Exception as e:
                    print(f"⚠️ 新字段准备时出现问题: {e}")
                
                # 创建任务
                task = OtaTask(**task_data)
                db.session.add(task)
                db.session.commit()
                
                self.test_tasks.append(task)
                print(f"✅ 带新字段的OTA任务创建成功: 任务ID={task.id}")
                
                # 验证新字段
                retrieved_task = OtaTask.query.get(task.id)
                
                # 使用兼容性方法检查字段
                detailed_status = retrieved_task.get_detailed_status()
                stage_info = retrieved_task.get_stage_info()
                retry_count = retrieved_task.get_retry_count()
                max_retries = retrieved_task.get_max_retries()
                
                print(f"📊 新字段值:")
                print(f"  detailed_status: {detailed_status}")
                print(f"  stage_info: {stage_info}")
                print(f"  retry_count: {retry_count}")
                print(f"  max_retries: {max_retries}")
                
                print("✅ 新字段兼容性验证通过")
                return True
                
            except Exception as e:
                print(f"❌ 新字段兼容性测试失败: {e}")
                return False
    
    def test_to_dict_method(self):
        """测试to_dict方法的兼容性"""
        print("\n🧪 测试to_dict方法兼容性...")
        
        with self.app.app_context():
            try:
                if not self.test_tasks:
                    print("⚠️ 没有测试任务，跳过to_dict测试")
                    return True
                
                task = self.test_tasks[0]
                
                # 测试to_dict方法
                task_dict = task.to_dict()
                
                # 验证必需字段
                required_fields = [
                    'id', 'device_id', 'firmware_path', 'firmware_version',
                    'status', 'detailed_status', 'progress', 'error_message',
                    'stage_info', 'retry_count', 'max_retries',
                    'created_at', 'updated_at'
                ]
                
                missing_fields = []
                for field in required_fields:
                    if field not in task_dict:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"❌ to_dict缺少字段: {missing_fields}")
                    return False
                
                print("📊 to_dict输出:")
                for key, value in task_dict.items():
                    print(f"  {key}: {value}")
                
                print("✅ to_dict方法兼容性验证通过")
                return True
                
            except Exception as e:
                print(f"❌ to_dict方法测试失败: {e}")
                return False
    
    def test_database_session_operations(self):
        """测试数据库会话操作"""
        print("\n🧪 测试数据库会话操作...")
        
        with self.app.app_context():
            try:
                from services.database_session_manager import thread_safe_db_ops
                
                if not self.test_tasks:
                    print("⚠️ 没有测试任务，跳过会话操作测试")
                    return True
                
                task = self.test_tasks[0]
                
                # 测试状态更新
                success = thread_safe_db_ops.update_task_status(
                    task_id=task.id,
                    status="进行中",
                    progress=50,
                    detailed_status="连接设备中",
                    stage_info="正在连接目标设备"
                )
                
                if success:
                    print("✅ 线程安全状态更新成功")
                else:
                    print("❌ 线程安全状态更新失败")
                    return False
                
                # 验证更新结果
                updated_task = OtaTask.query.get(task.id)
                assert updated_task.status == "进行中", "状态更新失败"
                assert updated_task.progress == 50, "进度更新失败"
                
                print("✅ 状态更新验证通过")
                
                # 测试获取任务信息
                task_info = thread_safe_db_ops.get_task_info(task.id)
                if task_info:
                    print(f"✅ 任务信息获取成功: {task_info['id']}")
                else:
                    print("❌ 任务信息获取失败")
                    return False
                
                return True
                
            except Exception as e:
                print(f"❌ 数据库会话操作测试失败: {e}")
                return False
    
    def test_parallel_ota_service(self):
        """测试并行OTA服务"""
        print("\n🧪 测试并行OTA服务...")
        
        with self.app.app_context():
            try:
                from services.parallel_ota_service import initialize_parallel_ota_service
                
                # 初始化并行OTA服务
                success = initialize_parallel_ota_service(self.app, max_workers=2)
                if success:
                    print("✅ 并行OTA服务初始化成功")
                else:
                    print("❌ 并行OTA服务初始化失败")
                    return False
                
                # 测试服务统计
                from services.parallel_ota_service import get_ota_service_stats
                stats = get_ota_service_stats()
                print(f"📊 服务统计: {stats}")
                
                print("✅ 并行OTA服务测试通过")
                return True
                
            except Exception as e:
                print(f"❌ 并行OTA服务测试失败: {e}")
                return False
    
    def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        
        with self.app.app_context():
            try:
                # 删除测试任务
                for task in self.test_tasks:
                    db.session.delete(task)
                
                # 删除测试设备
                if self.test_device:
                    db.session.delete(self.test_device)
                
                db.session.commit()
                print("✅ 测试数据清理完成")
                
            except Exception as e:
                print(f"⚠️ 清理测试数据时出现问题: {e}")
                db.session.rollback()
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始数据库迁移测试")
        print("=" * 60)
        
        try:
            self.setup()
            
            tests = [
                ("基本OTA任务创建", self.test_basic_ota_task_creation),
                ("新字段兼容性", self.test_new_fields_compatibility),
                ("to_dict方法兼容性", self.test_to_dict_method),
                ("数据库会话操作", self.test_database_session_operations),
                ("并行OTA服务", self.test_parallel_ota_service),
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                try:
                    if test_func():
                        print(f"✅ {test_name} 测试通过")
                        passed += 1
                    else:
                        print(f"❌ {test_name} 测试失败")
                except Exception as e:
                    print(f"💥 {test_name} 测试异常: {e}")
            
            print("\n" + "=" * 60)
            print(f"🏁 测试完成: {passed}/{total} 通过")
            
            if passed == total:
                print("🎉 所有测试通过！数据库迁移兼容性良好")
                return True
            else:
                print("⚠️ 部分测试失败，请检查数据库迁移状态")
                return False
                
        except Exception as e:
            print(f"💥 测试套件异常: {e}")
            return False
        
        finally:
            self.cleanup()


def main():
    """主函数"""
    tester = DatabaseMigrationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎊 数据库迁移测试全部通过！")
        sys.exit(0)
    else:
        print("\n💔 数据库迁移测试存在问题")
        sys.exit(1)


if __name__ == "__main__":
    main()
