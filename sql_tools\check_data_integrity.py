#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查SQLite数据库中的数据完整性问题
"""
import sqlite3
import os

def check_data_integrity():
    """检查数据完整性"""
    db_path = r'C:\Users\<USER>\Desktop\WorkPlace\Charging_Pile\web_admin\instance\charging_pile.db'
    
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("检查数据完整性问题...")
        print("=" * 50)
        
        # 检查device表的ID范围
        cursor.execute("SELECT MIN(id), MAX(id), COUNT(*) FROM device")
        device_stats = cursor.fetchone()
        print(f"Device表: 最小ID={device_stats[0]}, 最大ID={device_stats[1]}, 总数={device_stats[2]}")
        
        # 检查device_parameter表中引用的device_id
        cursor.execute("SELECT MIN(device_id), MAX(device_id), COUNT(DISTINCT device_id) FROM device_parameter")
        param_stats = cursor.fetchone()
        print(f"DeviceParameter表: 最小device_id={param_stats[0]}, 最大device_id={param_stats[1]}, 不同device_id数量={param_stats[2]}")
        
        # 查找device_parameter中不存在于device表的device_id
        cursor.execute("""
            SELECT DISTINCT dp.device_id, COUNT(*) as count
            FROM device_parameter dp
            LEFT JOIN device d ON dp.device_id = d.id
            WHERE d.id IS NULL
            GROUP BY dp.device_id
            ORDER BY dp.device_id
        """)
        orphaned_params = cursor.fetchall()
        
        if orphaned_params:
            print(f"\n发现 {len(orphaned_params)} 个孤立的device_parameter记录:")
            for device_id, count in orphaned_params:
                print(f"  device_id={device_id}: {count} 条记录")
        else:
            print("\n✓ device_parameter表中没有孤立记录")
        
        # 检查其他外键关系
        print("\n检查其他外键关系:")
        
        # login_logs -> users
        cursor.execute("""
            SELECT COUNT(*)
            FROM login_logs ll
            LEFT JOIN users u ON ll.user_id = u.id
            WHERE ll.user_id IS NOT NULL AND u.id IS NULL
        """)
        orphaned_login_logs = cursor.fetchone()[0]
        print(f"login_logs中孤立记录: {orphaned_login_logs}")
        
        # ota_task -> device
        cursor.execute("""
            SELECT COUNT(*)
            FROM ota_task ot
            LEFT JOIN device d ON ot.device_id = d.id
            WHERE d.id IS NULL
        """)
        orphaned_ota_tasks = cursor.fetchone()[0]
        print(f"ota_task中孤立记录: {orphaned_ota_tasks}")
        
        # device_locations -> device (通过device_id字符串)
        cursor.execute("""
            SELECT COUNT(*)
            FROM device_locations dl
            LEFT JOIN device d ON dl.device_id = d.device_id
            WHERE d.device_id IS NULL
        """)
        orphaned_locations = cursor.fetchone()[0]
        print(f"device_locations中孤立记录: {orphaned_locations}")
        
        # download_orders -> users
        cursor.execute("""
            SELECT COUNT(*)
            FROM download_orders do
            LEFT JOIN users u ON do.user_id = u.id
            WHERE u.id IS NULL
        """)
        orphaned_orders_users = cursor.fetchone()[0]
        print(f"download_orders中孤立用户记录: {orphaned_orders_users}")
        
        # download_orders -> paid_downloads
        cursor.execute("""
            SELECT COUNT(*)
            FROM download_orders do
            LEFT JOIN paid_downloads pd ON do.download_id = pd.id
            WHERE pd.id IS NULL
        """)
        orphaned_orders_downloads = cursor.fetchone()[0]
        print(f"download_orders中孤立下载记录: {orphaned_orders_downloads}")
        
        # debug_script -> device
        cursor.execute("""
            SELECT COUNT(*)
            FROM debug_script ds
            LEFT JOIN device d ON ds.device_id = d.id
            WHERE d.id IS NULL
        """)
        orphaned_debug_scripts = cursor.fetchone()[0]
        print(f"debug_script中孤立记录: {orphaned_debug_scripts}")
        
        conn.close()
        print("\n数据完整性检查完成")
        
        # 返回是否有数据完整性问题
        has_issues = (len(orphaned_params) > 0 or orphaned_login_logs > 0 or 
                     orphaned_ota_tasks > 0 or orphaned_locations > 0 or 
                     orphaned_orders_users > 0 or orphaned_orders_downloads > 0 or 
                     orphaned_debug_scripts > 0)
        
        return not has_issues, orphaned_params
        
    except Exception as e:
        print(f'检查数据完整性时出错: {e}')
        return False, []

if __name__ == '__main__':
    is_clean, orphaned = check_data_integrity()
    if not is_clean:
        print("\n⚠️  发现数据完整性问题，需要在迁移前清理")
    else:
        print("\n✓ 数据完整性良好，可以直接迁移")
