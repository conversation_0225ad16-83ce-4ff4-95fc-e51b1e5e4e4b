<!-- 批量OTA升级模态框 -->
<div class="modal fade" id="batchOtaModal" tabindex="-1" aria-labelledby="batchOtaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered animate__animated animate__fadeInDown animate__faster">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchOtaModalLabel">批量OTA升级</h5>
                <button type="button" class="btn-close" id="batchOtaModalClose" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="batchOtaForm" action="/ota/start" method="post">
                    <div class="mb-3">
                        <label class="form-label">已选设备</label>
                        <div id="selectedDevicesList_ota" class="list-group">
                            <!-- 这里将通过JavaScript动态填充选中的设备 -->
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="firmwareSelect" class="form-label">选择固件</label>
                        <select class="form-select" id="firmwareSelect" name="firmware_id" required>
                            <option value="">请选择固件...</option>
                            {% for firmware in firmwares %}
                            <option value="{{ firmware.id }}" data-version="{{ firmware.version }}" data-description="{{ firmware.description }}">
                                {{ firmware.name }} (v{{ firmware.version }})
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">从固件库中选择要升级的固件版本</div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancelBatchOta">取消</button>
                        <button type="submit" class="btn btn-primary">开始升级</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 批量OTA模态框实例
let batchOtaModalInstance = null;

// 显示批量OTA升级模态框
function showBatchOtaModal() {
    // 获取所有选中的设备
    const selectedDevices = [];
    const checkboxes = document.querySelectorAll('tbody .device-checkbox:checked');

    if (checkboxes.length === 0) {
        alert('请先选择要升级的设备');
        return;
    }

    checkboxes.forEach(function (checkbox) {
        const deviceId = checkbox.value;
        const deviceRow = checkbox.closest('tr');
        const deviceIdCell = deviceRow.querySelector('td:nth-child(2)');
        const deviceRemarkCell = deviceRow.querySelector('td:nth-child(3)');

        selectedDevices.push({
            id: deviceId,
            deviceId: deviceIdCell.textContent.trim(),
            remark: deviceRemarkCell.textContent.trim()
        });
    });

    // 更新选中设备列表
    const devicesList = document.getElementById('selectedDevicesList_ota');
    devicesList.innerHTML = '';

    // 添加隐藏的设备ID字段
    const form = document.getElementById('batchOtaForm');
    // 移除之前可能存在的隐藏字段
    const existingInputs = form.querySelectorAll('input[name="device_ids[]"]');
    existingInputs.forEach(input => input.remove());

    // 添加新的隐藏字段和设备列表项
    selectedDevices.forEach(function (device) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'device_ids[]';
        input.value = device.id;
        form.appendChild(input);

        // 添加设备到列表
        const deviceItem = document.createElement('div');
        deviceItem.className = 'list-group-item animate__animated animate__fadeIn';
        deviceItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${device.deviceId}</strong>
                    <div class="text-muted small">${device.remark}</div>
                </div>
                <span class="badge bg-primary">已选择</span>
            </div>
        `;
        devicesList.appendChild(deviceItem);
    });

    // 显示模态框
    if (batchOtaModalInstance) {
        batchOtaModalInstance.dispose();
    }

    // 重置模态框动画类
    const modalDialog = document.querySelector('#batchOtaModal .modal-dialog');
    modalDialog.classList.remove('animate__fadeOutUp');
    modalDialog.classList.add('animate__fadeInDown');

    // 如果已存在实例，先销毁
    if (batchOtaModalInstance) {
        batchOtaModalInstance.dispose();
    }

    // 创建新的模态框实例
    batchOtaModalInstance = new bootstrap.Modal(document.getElementById('batchOtaModal'), {
        backdrop: true, // 允许点击背景关闭
        keyboard: true  // 允许按ESC关闭
    });

    // 显示模态框
    batchOtaModalInstance.show();
}

// 关闭批量OTA模态框
function closeBatchOtaModal() {
    if (batchOtaModalInstance) {
        // 添加关闭动画
        const modalDialog = document.querySelector('#batchOtaModal .modal-dialog');
        modalDialog.classList.remove('animate__fadeInDown');
        modalDialog.classList.add('animate__fadeOutUp');

        // 等待动画完成后关闭
        setTimeout(() => {
            batchOtaModalInstance.hide();

            // 延迟一小段时间后移除背景，确保动画完成
            setTimeout(() => {
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }, 150);
        }, 300);
    }
}

// 初始化批量OTA相关功能
function initBatchOta() {
    // 确保模态框元素存在
    const modalElement = document.getElementById('batchOtaModal');
    if (!modalElement) {
        console.error('找不到批量升级模态框元素');
        return;
    }

    // 为批量升级按钮添加点击事件
    const batchOtaButtons = document.querySelectorAll('.batch-ota-btn');
    batchOtaButtons.forEach(button => {
        button.addEventListener('click', function (e) {
            e.preventDefault();
            showBatchOtaModal();
        });
    });

    // 为关闭按钮添加事件
    const closeButton = document.getElementById('batchOtaModalClose');
    const cancelButton = document.getElementById('cancelBatchOta');
    
    if (closeButton) {
        closeButton.addEventListener('click', closeBatchOtaModal);
    }
    if (cancelButton) {
        cancelButton.addEventListener('click', closeBatchOtaModal);
    }

    // 处理批量OTA表单提交
    const batchOtaForm = document.getElementById('batchOtaForm');
    if (batchOtaForm) {
        batchOtaForm.addEventListener('submit', async function (e) {
            e.preventDefault();
            const formData = new FormData(this);
            const selectedDevices = Array.from(document.querySelectorAll('tbody .device-checkbox:checked')).map(cb => cb.value);
            const firmwareId = formData.get('firmware_id');
            
            if (!firmwareId) {
                alert('请选择要升级的固件');
                return;
            }
            
            try {
                const response = await fetch('/ota/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        device_ids: selectedDevices,
                        firmware_id: firmwareId
                    })
                });
                const data = await response.json();

                if (data.success) {
                    closeBatchOtaModal();
                    alert('OTA升级任务已创建');
                    location.reload();
                } else {
                    alert(data.message || '创建OTA任务失败');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('创建OTA任务失败，请重试');
            }
        });
    }

    // 添加固件选择事件监听
    const firmwareSelect = document.getElementById('firmwareSelect');
    if (firmwareSelect) {
        firmwareSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const version = selectedOption.dataset.version;
            const description = selectedOption.dataset.description;
            
            if (version && description) {
                console.log(`已选择固件版本: ${version}`);
                console.log(`固件描述: ${description}`);
            }
        });
    }

    // 监听模态框事件，防止多次触发
    document.getElementById('batchOtaModal').addEventListener('hidden.bs.modal', function (e) {
        if (!e.target.classList.contains('animating')) {
            document.body.classList.remove('modal-open');
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }
    });
}

// 在页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加Animate.css到页面
    if (!document.querySelector('link[href*="animate.css"]')) {
        const animateCss = document.createElement('link');
        animateCss.rel = 'stylesheet';
        animateCss.href = 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css';
        document.head.appendChild(animateCss);
    }

    // 添加自定义样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-backdrop {
            transition: opacity 0.3s ease-in-out;
        }
        .modal.fade .modal-dialog {
            transition: transform 0.3s ease-out;
        }
        .list-group-item {
            transition: all 0.2s ease;
        }
        .list-group-item:hover {
            transform: translateX(5px);
            background-color: rgba(13, 110, 253, 0.05);
        }
    `;
    document.head.appendChild(style);

    // 初始化批量OTA功能
    initBatchOta();
});
</script> 