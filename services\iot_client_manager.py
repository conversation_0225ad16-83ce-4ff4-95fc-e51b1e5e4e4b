import threading
from iot_client.iot_client import IoTClient
from iot_client.platform.ali_mqtt_client import AmqpConfig
from iot_client.platform.emqx_mqtt_client import EMQXConfig
from utils.logger import LoggerManager
import logging

# 获取日志记录器
logger = LoggerManager.get_logger()

# 添加IoT客户端管理类
class IoTClientManager:
    """IoT客户端管理类 - 单例模式"""

    _instance = None
    _running = False
    _lock = threading.Lock()

    @classmethod
    def initialize(cls):
        """初始化IoT客户端管理器"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    # 创建配置
                    config = AmqpConfig()
                    exqx_config = EMQXConfig()
                    # topic_filters = ["^/[^/]+/[^/]+/user/update$", "^/[^/]+/[^/]+/user/ota_ack$"]
                    topic_filters = ["^/[^/]+/[^/]+/user/ota_ack$"]
                    cls._instance = IoTClient(topic_filters, logger, config, exqx_config)
                    # 关闭 stomp.py 的 DEBUG 日志
                    logging.getLogger("stomp.py").setLevel(logging.INFO)
                    logger.info("IoT客户端管理器已初始化")

    @classmethod
    def start(cls):
        """启动IoT客户端"""
        if cls._instance is None:
            cls.initialize()

        if not cls._running:
            cls._instance.start()
            cls._running = True
            logger.info("IoT客户端已启动")
            return True
        return False

    @classmethod
    def stop(cls):
        """停止IoT客户端"""
        if cls._running and cls._instance:
            cls._instance.stop()
            cls._running = False
            logger.info("IoT客户端已停止")
            return True
        return False

    @classmethod
    def is_running(cls):
        """检查IoT客户端是否运行中"""
        return cls._running

    @classmethod
    def get_instance(cls):
        """获取IoT客户端实例"""
        if not cls._running:
            raise RuntimeError("IoT客户端未启动")
        return cls._instance

    @classmethod
    def get_status(cls):
        """获取IoT客户端状态"""
        if cls._instance is None:
            return "未初始化"
        elif cls._running:
            return "运行中"
        else:
            return "已停止"
