from datetime import datetime
from models.database import db

class OtaTask(db.Model):
    """OTA任务模型"""
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.<PERSON>ey('device.id'), nullable=False)
    firmware_path = db.Column(db.String(255), nullable=False)
    firmware_version = db.Column(db.String(20), nullable=False)
    status = db.Column(db.String(20), default="等待中")  # 等待中, 进行中, 成功, 失败
    progress = db.Column(db.Integer, default=0)  # 0-100
    error_message = db.Column(db.Text, default="")

    # 新增字段支持并行OTA
    detailed_status = db.Column(db.String(50), default="等待中")  # 详细状态
    stage_info = db.Column(db.Text, default="")  # 阶段信息
    retry_count = db.Column(db.Integer, default=0)  # 重试次数
    max_retries = db.Column(db.Integer, default=3)  # 最大重试次数
    started_at = db.Column(db.DateTime, nullable=True)  # 开始时间
    completed_at = db.Column(db.DateTime, nullable=True)  # 完成时间

    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    device = db.relationship('Device', backref=db.backref('ota_task', lazy=True))

    def to_dict(self):
        """转换为字典格式（兼容旧数据）"""
        return {
            'id': self.id,
            'device_id': self.device_id,
            'device_name': self.device.device_id if self.device else None,
            'firmware_path': self.firmware_path,
            'firmware_version': self.firmware_version,
            'status': self.status,
            'detailed_status': getattr(self, 'detailed_status', None) or self.status,
            'progress': self.progress,
            'error_message': self.error_message or '',
            'stage_info': getattr(self, 'stage_info', None) or '',
            'retry_count': getattr(self, 'retry_count', None) or 0,
            'max_retries': getattr(self, 'max_retries', None) or 3,
            'started_at': self.started_at.isoformat() if getattr(self, 'started_at', None) else None,
            'completed_at': self.completed_at.isoformat() if getattr(self, 'completed_at', None) else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def get_detailed_status(self):
        """获取详细状态（兼容旧数据）"""
        return getattr(self, 'detailed_status', None) or self.status

    def get_stage_info(self):
        """获取阶段信息（兼容旧数据）"""
        return getattr(self, 'stage_info', None) or ''

    def get_retry_count(self):
        """获取重试次数（兼容旧数据）"""
        return getattr(self, 'retry_count', None) or 0

    def get_max_retries(self):
        """获取最大重试次数（兼容旧数据）"""
        return getattr(self, 'max_retries', None) or 3