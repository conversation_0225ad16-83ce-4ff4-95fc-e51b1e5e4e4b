#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查SQLite数据库结构的脚本
"""
import sqlite3
import os

def check_database_structure():
    """检查数据库结构"""
    db_path = r'C:\Users\<USER>\Desktop\WorkPlace\Charging_Pile\web_admin\instance\charging_pile.db'
    
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print('数据库中的表:')
        for table in tables:
            print(f'  - {table[0]}')
        
        # 获取每个表的结构和数据量
        for table in tables:
            table_name = table[0]
            print(f'\n表 {table_name} 的结构:')
            cursor.execute(f'PRAGMA table_info({table_name})')
            columns = cursor.fetchall()
            for col in columns:
                print(f'  {col[1]} {col[2]} (PK: {col[5]}, NOT NULL: {col[3]}, DEFAULT: {col[4]})')
            
            # 获取数据量
            cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
            count = cursor.fetchone()[0]
            print(f'  数据量: {count} 行')
            
            # 获取外键信息
            cursor.execute(f'PRAGMA foreign_key_list({table_name})')
            fks = cursor.fetchall()
            if fks:
                print(f'  外键:')
                for fk in fks:
                    print(f'    {fk[3]} -> {fk[2]}.{fk[4]}')
        
        conn.close()
        print('\n数据库结构检查完成')
        
    except Exception as e:
        print(f'检查数据库时出错: {e}')

if __name__ == '__main__':
    check_database_structure()
