"""
寄存器地址定义模块

本模块定义了设备寄存器地址常量和相关工具函数，用于设备通信和寄存器操作。
主要包含以下功能：
1. 定义所有寄存器地址常量
2. 提供寄存器名称和地址的映射关系
3. 提供获取寄存器名称的工具函数

使用示例:
    from reg_addr import RegAddr
    
    # 获取寄存器地址
    temp_reg_addr = RegAddr.REG_TEMP1
    
    # 获取寄存器名称
    reg_name = RegAddr.get_reg_name(RegAddr.REG_CSQ)
"""

# 寄存器地址定义
class RegAddr:
    REG_T1 = 0
    REG_T2 = 1
    REG_T3 = 2
    REG_T4 = 3
    REG_T5 = 4
    REG_T6 = 5
    REG_T7 = 6
    REG_T8 = 7
    REG_T9 = 8
    REG_T10 = 9
    REG_P1 = 10
    REG_P2 = 11
    REG_P3 = 12
    REG_P4 = 13
    REG_P5 = 14
    REG_P6 = 15
    REG_P7 = 16
    REG_P8 = 17
    REG_T11 = 18
    REG_CTRL1 = 19
    REG_TEMP1 = 20
    REG_BOOT_CNT = 21
    REG_VERSION_H = 22
    REG_VERSION_L = 23
    REG_PERSENTAGE = 24
    REG_CSQ = 25

    REG_LOCATION_CODE = 26
    REG_LOCATION_LATITUDE_H = 27
    REG_LOCATION_LATITUDE_L = 28
    REG_LOCATION_LONGITUDE_H = 29
    REG_LOCATION_LONGITUDE_L = 30
    APP_MAIN_REG_NUM = 26
    REG_ERROR_CNT1 = 31
    REG_ERROR_CNT2 = 32
    REG_ERROR_CNT3 = 33
    REG_ERROR_CNT4 = 34
    APP_MAIN_SYSINFO_VARIABLE_REG_NUM = 12
    
    # 寄存器名称映射表
    REG_NAMES = {
        REG_T1: "REG_T1",
        REG_T2: "REG_T2",
        REG_T3: "REG_T3",
        REG_T4: "REG_T4",
        REG_T5: "REG_T5",
        REG_T6: "REG_T6",
        REG_T7: "REG_T7",
        REG_T8: "REG_T8",
        REG_T9: "REG_T9",
        REG_T10: "REG_T10",
        REG_P1: "REG_P1",
        REG_P2: "REG_P2",
        REG_P3: "REG_P3",
        REG_P4: "REG_P4",
        REG_P5: "REG_P5",
        REG_P6: "REG_P6",
        REG_P7: "REG_P7",
        REG_P8: "REG_P8",
        REG_T11: "REG_T11",
        REG_CTRL1: "REG_CTRL1",
        REG_TEMP1: "REG_TEMP1",
        REG_BOOT_CNT: "REG_BOOT_CNT",
        REG_VERSION_H: "REG_VERSION_H",
        REG_VERSION_L: "REG_VERSION_L",
        REG_PERSENTAGE: "REG_PERSENTAGE",
        REG_CSQ: "REG_CSQ"
    }

    # 寄存器描述
    REG_DESC = {
        "REG_T1": "长时间未插入充电器检测时间(单位：s)",
        "REG_T2": "功率大于0连续时间，判定为已连接充电器(单位：s)",
        "REG_T3": "浮充时间，大于该时间判定为电量已满(单位：s)",
        "REG_T4": "功率超过限制判定时间(单位：s)",
        "REG_T5": "总功率超过限制触发时间(单位：ms)",
        "REG_T6": "温度超过阈值判定时间(单位：s)",
        "REG_T7": "初始单个口功率过大判定时间(单位：ms)",
        "REG_T8": "充电过程中继电器开路状态判断为中控断电的时间(单位：ms)",
        "REG_T9": "首次进入充电过程中功率突降为0时的浮充时间(单位：s)",
        "REG_T10": "无线充电浮充时间(单位：s)",
        "REG_P1": "浮充功率阈值(单位：W)",
        "REG_P2": "单口充电过程中的功率限制(单位：W)",
        "REG_P3": "单口充电过程中的安全功率限制(单位：W)",
        "REG_P4": "总功率限制(单位：W)",
        "REG_P5": "单口初始安全功率限制(单位：W)",
        "REG_P6": "启动充电后检测充电负载存在阈值(单位：W)",
        "REG_P7": "无线充电浮充功率阈值(单位：W)",
        "REG_P8": "判断是否接入用电设备的阈值，小于这个阈值判定为用电设备断开与插座的连接(单位：V5板子为BL0910的有功功率的寄存器值，V2板子为mW)",
        "REG_T11": "拔出充电器的判定时间(单位：秒)",
        "REG_CTRL1": "控制寄存器，bit0: 控制SIM卡拔出功能，bit1: 控制LED闪烁模式。",
        "REG_TEMP1": "过温保护阈值(单位：℃)",
        "REG_BOOT_CNT": "启动计数",
        "REG_VERSION_H": "版本号高字节",
        "REG_VERSION_L": "版本号低字节",
        "REG_PERSENTAGE": "拔出插头判定百分比(单位：%)",
        "REG_CSQ": "信号强度(CSQ)和误码率(BER)"
    }
    
    @classmethod
    def get_reg_name(cls, reg_addr: int) -> str:
        """获取寄存器名称"""
        return cls.REG_NAMES.get(reg_addr, f"未知寄存器{reg_addr}")

    @classmethod
    def get_reg_desc(cls, reg_name: str) -> str:
        """获取寄存器描述"""
        return cls.REG_DESC.get(reg_name, f"未知寄存器{reg_name}")
