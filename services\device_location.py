from models.device_location import DeviceLocation
from models.database import db
from utils.logger import setup_logging

logger = setup_logging()

def update_device_location_db(device_id:str, latitude:float, longitude:float, address:str):
    """更新设备位置信息"""
    location = DeviceLocation.query.filter_by(device_id=device_id).first()
    
    if not location:
        location = DeviceLocation(device_id=device_id)
        db.session.add(location)
    
    location.latitude = latitude
    location.longitude = longitude
    location.address = address
    
    db.session.commit()
    logger.info(f"设备 {device_id} 的位置信息已更新到数据库")
    return True

