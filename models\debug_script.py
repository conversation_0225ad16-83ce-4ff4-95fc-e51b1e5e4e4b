from datetime import datetime
from models.database import db

class DebugScript(db.Model):
    """调试脚本模型，用于存储设备调试脚本的配置和状态"""
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.<PERSON>teger, db.<PERSON>ey('device.id'), nullable=False)
    enabled = db.Column(db.<PERSON>, default=False)  # 是否启用
    frequency = db.Column(db.Integer, default=60)  # 执行频率（秒）
    total_executions = db.Column(db.Integer, default=0)  # 总执行次数
    successful_executions = db.Column(db.Integer, default=0)  # 成功执行次数
    last_execution_time = db.Column(db.DateTime)  # 最后执行时间
    last_execution_status = db.Column(db.String(20))  # 最后执行状态
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联设备
    device = db.relationship('Device', backref=db.backref('debug_script', uselist=False))
    
    def __repr__(self):
        return f'<DebugScript {self.id}: device_id={self.device_id}, enabled={self.enabled}>'
