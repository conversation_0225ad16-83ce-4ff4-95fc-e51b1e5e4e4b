{% extends "base.html" %}

{% block title %}{{ '编辑设备' if device else '添加设备' }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>{{ '编辑设备' if device else '添加设备' }}</h2>
    
    <div class="card mt-4">
        <div class="card-body">
            <form method="POST">
                <div class="mb-3">
                    <label for="device_id" class="form-label">设备ID</label>
                    <input type="text" class="form-control" id="device_id" name="device_id" value="{{ device.device_id if device else '' }}" required>
                    <div class="form-text">设备的唯一标识符</div>
                </div>
                
                <div class="mb-3">
                    <label for="device_remark" class="form-label">设备备注</label>
                    <textarea class="form-control" id="device_remark" name="device_remark" rows="3">{{ device.device_remark if device else '' }}</textarea>
                    <div class="form-text">设备的备注信息</div>
                </div>
                
                <div class="mb-3">
                    <label for="product_key" class="form-label">产品密钥</label>
                    <input type="text" class="form-control" id="product_key" name="product_key" value="{{ device.product_key if device else '' }}" required>
                    <div class="form-text">设备的产品密钥</div>
                </div>
                
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <a href="{{ url_for('device.devices') }}" class="btn btn-secondary">返回</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 