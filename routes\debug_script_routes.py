#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试脚本路由
提供调试脚本管理和功率数据查询的API接口
"""

from flask import Blueprint, render_template, request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import json
import os
import re
from models.device import Device
from models.debug_script import DebugScript
from models.database import db
from services.debug_script_manager import debug_script_manager
from services.influxdb_service import influxdb_service
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
debug_script_bp = Blueprint('debug_script', __name__, url_prefix='/debug_script')

@debug_script_bp.route('/status/<int:device_id>', methods=['GET'])
@login_required
def get_script_status(device_id):
    """获取设备调试脚本状态"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取脚本状态
        status = debug_script_manager.get_script_status(device_id)
        logger.info(f"设备 {device_id} 的调试脚本状态: {status}")
        return jsonify(status)
    except Exception as e:
        logger.error(f"获取调试脚本状态异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/start/<int:device_id>', methods=['POST'])
@login_required
def start_script(device_id):
    """启动设备调试脚本"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取请求参数
        data = request.get_json()
        if not data or 'frequency' not in data:
            return jsonify({'error': '缺少frequency参数'}), 400

        frequency = int(data['frequency'])
        if frequency < 5:
            return jsonify({'error': '频率不能小于5秒'}), 400

        # 启动脚本
        success = debug_script_manager.start_script(device_id, frequency)

        if success:
            return jsonify({'success': True, 'message': f'设备 {device.device_id} 的调试脚本已启动'})
        else:
            return jsonify({'success': False, 'message': '启动调试脚本失败'}), 500
    except Exception as e:
        logger.error(f"启动调试脚本异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/stop/<int:device_id>', methods=['POST'])
@login_required
def stop_script(device_id):
    """停止设备调试脚本"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 停止脚本
        success = debug_script_manager.stop_script(device_id)

        if success:
            return jsonify({'success': True, 'message': f'设备 {device.device_id} 的调试脚本已停止'})
        else:
            return jsonify({'success': False, 'message': '停止调试脚本失败'}), 500
    except Exception as e:
        logger.error(f"停止调试脚本异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/power_data/<int:device_id>', methods=['GET'])
@login_required
def get_power_data(device_id):
    """获取设备功率数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取查询参数
        date_str = request.args.get('date')
        if date_str:
            try:
                # 解析日期字符串
                date = datetime.strptime(date_str, '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(days=1)
            except ValueError:
                return jsonify({'error': '日期格式无效，应为YYYY-MM-DD'}), 400
        else:
            # 默认查询当天数据
            now = datetime.now()
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now

        # 查询功率数据
        power_data = influxdb_service.query_power_data(device.device_id, start_time, end_time)

        return jsonify({
            'device_id': device.device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'power_data': power_data
        })
    except Exception as e:
        logger.error(f"获取功率数据异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/power_history/<int:device_id>', methods=['GET'])
@login_required
def power_history_page(device_id):
    """功率历史数据页面"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取今天的日期，格式为YYYY-MM-DD
        today_date = datetime.now().strftime('%Y-%m-%d')

        return render_template('power_history.html', device=device, today_date=today_date)
    except Exception as e:
        logger.error(f"访问功率历史数据页面异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/temperature_history/<int:device_id>', methods=['GET'])
@login_required
def temperature_history_page(device_id):
    """温度历史数据页面"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取今天的日期，格式为YYYY-MM-DD
        today_date = datetime.now().strftime('%Y-%m-%d')

        return render_template('temperature_history.html', device=device, today_date=today_date)
    except Exception as e:
        logger.error(f"访问温度历史数据页面异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/voltage_history/<int:device_id>', methods=['GET'])
@login_required
def voltage_history_page(device_id):
    """电压历史数据页面"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取今天的日期，格式为YYYY-MM-DD
        today_date = datetime.now().strftime('%Y-%m-%d')

        return render_template('voltage_history.html', device=device, today_date=today_date)
    except Exception as e:
        logger.error(f"访问电压历史数据页面异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/csq_history/<int:device_id>', methods=['GET'])
@login_required
def csq_history_page(device_id):
    """信号质量历史数据页面"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取今天的日期，格式为YYYY-MM-DD
        today_date = datetime.now().strftime('%Y-%m-%d')

        return render_template('csq_history.html', device=device, today_date=today_date)
    except Exception as e:
        logger.error(f"访问信号质量历史数据页面异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/temperature_data/<int:device_id>', methods=['GET'])
@login_required
def temperature_data(device_id):
    """获取设备温度数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取查询参数
        date_str = request.args.get('date')
        if date_str:
            try:
                # 解析日期字符串
                date = datetime.strptime(date_str, '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(days=1)
            except ValueError:
                return jsonify({'error': '日期格式无效，应为YYYY-MM-DD'}), 400
        else:
            # 默认查询当天数据
            now = datetime.now()
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now

        # 查询温度数据
        temperature_data = influxdb_service.query_temperature_data(device.device_id, start_time, end_time)

        return jsonify({
            'device_id': device.device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'temperature_data': temperature_data
        })
    except Exception as e:
        logger.error(f"获取温度数据异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/voltage_data/<int:device_id>', methods=['GET'])
@login_required
def voltage_data(device_id):
    """获取设备电压数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取查询参数
        date_str = request.args.get('date')
        if date_str:
            try:
                # 解析日期字符串
                date = datetime.strptime(date_str, '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(days=1)
            except ValueError:
                return jsonify({'error': '日期格式无效，应为YYYY-MM-DD'}), 400
        else:
            # 默认查询当天数据
            now = datetime.now()
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now

        # 查询电压数据
        voltage_data = influxdb_service.query_voltage_data(device.device_id, start_time, end_time)

        return jsonify({
            'device_id': device.device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'voltage_data': voltage_data
        })
    except Exception as e:
        logger.error(f"获取电压数据异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/csq_data/<int:device_id>', methods=['GET'])
@login_required
def csq_data(device_id):
    """获取设备信号质量数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取查询参数
        date_str = request.args.get('date')
        if date_str:
            try:
                # 解析日期字符串
                date = datetime.strptime(date_str, '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(days=1)
            except ValueError:
                return jsonify({'error': '日期格式无效，应为YYYY-MM-DD'}), 400
        else:
            # 默认查询当天数据
            now = datetime.now()
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now

        # 查询信号质量数据
        csq_data = influxdb_service.query_csq_data(device.device_id, start_time, end_time)

        return jsonify({
            'device_id': device.device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'csq_data': csq_data
        })
    except Exception as e:
        logger.error(f"获取信号质量数据异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/api/device/<device_id>/data_dates', methods=['GET'])
@login_required
def get_device_data_dates(device_id):
    """获取设备有数据的日期列表"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 获取设备数据目录
        device_dir = os.path.join(current_app.config['INFLUXDB_DATA_PATH'], current_app.config['INFLUXDB_BUCKET'], device.device_id)
        if not os.path.exists(device_dir):
            return jsonify({'dates': []})

        # 获取所有日期目录
        dates = []
        logger.info(f"设备数据目录: {device_dir}")
        for date_dir in os.listdir(device_dir):
            if os.path.isdir(os.path.join(device_dir, date_dir)):
                # 检查该日期目录下是否有数据文件
                date_path = os.path.join(device_dir, date_dir)
                has_data = any(f.endswith('.csv') for f in os.listdir(date_path))
                if has_data:
                    dates.append(date_dir)
        
        return jsonify({'dates': sorted(dates)})
    except Exception as e:
        logger.error(f"获取设备数据日期列表失败: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/api/devices/batch_start_debug_script', methods=['POST'])
@login_required
def batch_start_debug_script():
    """批量启动调试脚本"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data or 'frequency' not in data:
            return jsonify({'error': '缺少必要参数'}), 400

        device_ids = data['device_ids']
        frequency = int(data['frequency'])

        if frequency < 5:
            return jsonify({'error': '频率不能小于5秒'}), 400

        results = []
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '设备不存在'
                    })
                    continue

                success = debug_script_manager.start_script(device_id, frequency)
                results.append({
                    'device_id': device_id,
                    'success': success,
                    'error': None if success else '启动失败'
                })
            except Exception as e:
                results.append({
                    'device_id': device_id,
                    'success': False,
                    'error': str(e)
                })

        return jsonify({
            'success': True,
            'results': results
        })
    except Exception as e:
        logger.error(f"批量启动调试脚本异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/api/devices/batch_stop_debug_script', methods=['POST'])
@login_required
def batch_stop_debug_script():
    """批量停止调试脚本"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data:
            return jsonify({'error': '缺少必要参数'}), 400

        device_ids = data['device_ids']
        results = []
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '设备不存在'
                    })
                    continue

                success = debug_script_manager.stop_script(device_id)
                results.append({
                    'device_id': device_id,
                    'success': success,
                    'error': None if success else '停止失败'
                })
            except Exception as e:
                results.append({
                    'device_id': device_id,
                    'success': False,
                    'error': str(e)
                })

        return jsonify({
            'success': True,
            'results': results
        })
    except Exception as e:
        logger.error(f"批量停止调试脚本异常: {e}")
        return jsonify({'error': str(e)}), 500 