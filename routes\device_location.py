from flask import Blueprint, render_template, jsonify, request
from models.device_location import DeviceLocation
from models.device import Device
from models.database import db
from services.device_location import update_device_location_db
bp = Blueprint('device_location', __name__)

@bp.route('/device/map')
def device_map():
    """设备地图页面"""
    return render_template('device/map.html')

@bp.route('/api/device/locations')
def get_device_locations():
    """获取所有设备的位置信息"""
    locations = DeviceLocation.query.all()
    return jsonify([{
        'id': loc.device.id,
        'device_id': loc.device_id,
        'latitude': loc.latitude,
        'longitude': loc.longitude,
        'address': loc.address,
        'device_name': loc.device.device_remark if loc.device else None
    } for loc in locations])

@bp.route('/api/device/location/<device_id>', methods=['POST'])
def update_device_location(device_id):
    """更新设备位置信息"""
    data = request.json
    latitude = data.get('latitude')
    longitude = data.get('longitude')
    address = data.get('address')

    ret = update_device_location_db(device_id, latitude, longitude, address)
    if ret:
        return jsonify({'message': '位置信息更新成功'})
    else:
        return jsonify({'message': '位置信息更新失败'}), 500

@bp.route('/api/device/location/<device_id>', methods=['DELETE'])
def delete_device_location(device_id):
    """删除设备位置信息"""
    try:
        location = DeviceLocation.query.filter_by(device_id=device_id).first()
        if location:
            db.session.delete(location)
            db.session.commit()
            return jsonify({'success': True, 'message': '位置信息删除成功'})
        else:
            return jsonify({'success': False, 'message': '设备位置信息不存在'}), 404
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'}), 500
