{% extends "base.html" %}

{% block title %}固件管理{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题和上传按钮 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0"><i class="fas fa-microchip text-primary"></i> 固件管理</h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadFirmwareModal">
                    <i class="fas fa-upload"></i> 上传固件
                </button>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-primary h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file-code fa-3x text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-muted mb-1">固件总数</h6>
                            <h3 class="mb-0">{{ firmwares|length }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-success h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle fa-3x text-success"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-muted mb-1">最新版本</h6>
                            <h3 class="mb-0">{{ latest_version }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-info h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-database fa-3x text-info"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-muted mb-1">总存储空间</h6>
                            <h3 class="mb-0">{{ total_size }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 固件列表 -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0"><i class="fas fa-list text-primary"></i> 固件列表</h5>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索固件...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>版本号</th>
                            <th>类别</th>
                            <th>描述</th>
                            <th>大小</th>
                            <th>CRC32</th>
                            <th>上传时间</th>
                            <th class="text-end">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for firmware in firmwares %}
                        <tr>
                            <td class="align-middle">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-code text-primary me-2"></i>
                                    {{ firmware.version }}
                                </div>
                            </td>
                            <td class="align-middle">{{ firmware.category }}</td>
                            <td class="align-middle">{{ firmware.description }}</td>
                            <td class="align-middle">{{ firmware.size }}</td>
                            <td class="align-middle">
                                <code class="text-muted">{{ firmware.crc32 }}</code>
                            </td>
                            <td class="align-middle">
                                <span class="text-muted">
                                    <i class="far fa-clock me-1"></i>
                                    {{ firmware.upload_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                </span>
                            </td>
                            <td class="align-middle text-end">
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-info" onclick="showFirmwareDetails('{{ firmware.id }}')" title="详情">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                    <a href="{{ url_for('firmware.download_firmware', id=firmware.id) }}" class="btn btn-sm btn-primary" title="下载">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button class="btn btn-sm btn-danger" onclick="deleteFirmware('{{ firmware.id }}')" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 上传固件模态框 -->
<div class="modal fade" id="uploadFirmwareModal" tabindex="-1" aria-labelledby="uploadFirmwareModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadFirmwareModalLabel">
                    <i class="fas fa-upload text-primary me-2"></i>上传固件
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="uploadFirmwareForm" action="{{ url_for('firmware.upload_firmware') }}" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="firmware_file" class="form-label">固件文件</label>
                        <div class="input-group">
                            <input type="file" class="form-control" id="firmware_file" name="firmware_file" required>
                        </div>
                        <div class="form-text">支持.bin格式的固件文件</div>
                    </div>
                    <div class="mb-3">
                        <label for="firmware_category" class="form-label">固件类别</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-tag"></i></span>
                            <input type="text" class="form-control" id="firmware_category" name="category" value="ECycle_G070B8" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="firmware_description" class="form-label">固件描述</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-align-left"></i></span>
                            <textarea class="form-control" id="firmware_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <small>系统将自动解析固件版本信息并计算CRC32校验值</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>上传
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 固件详情模态框 -->
<div class="modal fade" id="firmwareDetailsModal" tabindex="-1" aria-labelledby="firmwareDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="firmwareDetailsModalLabel">
                    <i class="fas fa-info-circle text-primary me-2"></i>固件详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th style="width: 30%">版本号</th>
                                <td id="detail_version"></td>
                            </tr>
                            <tr>
                                <th>类别</th>
                                <td id="detail_category"></td>
                            </tr>
                            <tr>
                                <th>描述</th>
                                <td id="detail_description"></td>
                            </tr>
                            <tr>
                                <th>文件大小</th>
                                <td id="detail_size"></td>
                            </tr>
                            <tr>
                                <th>CRC32</th>
                                <td><code id="detail_crc32"></code></td>
                            </tr>
                            <tr>
                                <th>上传时间</th>
                                <td id="detail_upload_time"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('keyup', function() {
    var input = this.value.toLowerCase();
    var rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(function(row) {
        var version = row.cells[0].textContent.toLowerCase();
        var category = row.cells[1].textContent.toLowerCase();
        var description = row.cells[2].textContent.toLowerCase();
        
        if (version.includes(input) || category.includes(input) || description.includes(input)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// 显示固件详情
function showFirmwareDetails(firmwareId) {
    fetch(`/firmware/${firmwareId}/details`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('detail_version').textContent = data.version;
            document.getElementById('detail_category').textContent = data.category;
            document.getElementById('detail_description').textContent = data.description;
            document.getElementById('detail_size').textContent = data.size;
            document.getElementById('detail_crc32').textContent = data.crc32;
            document.getElementById('detail_upload_time').textContent = data.upload_time;
            
            const modal = new bootstrap.Modal(document.getElementById('firmwareDetailsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('获取固件详情失败');
        });
}

// 删除固件
function deleteFirmware(firmwareId) {
    if (confirm('确定要删除此固件吗？此操作不可恢复。')) {
        fetch(`/firmware/${firmwareId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '删除固件失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除固件失败');
        });
    }
}

// 处理固件上传
document.getElementById('uploadFirmwareForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    
    fetch('/firmware/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '上传固件失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('上传固件失败');
    });
});
</script>
{% endblock %} 