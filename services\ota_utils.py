#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA工具类
提供OTA相关的通用工具函数
"""

import os
import hashlib
from typing import List, Tuple, Optional, Dict
from werkzeug.utils import secure_filename
from models.device import Device
from models.firmware import Firmware
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class FirmwareValidator:
    """固件验证器"""

    @staticmethod
    def validate_firmware_file(file_path: str) -> Tuple[bool, str]:
        """
        验证固件文件

        Args:
            file_path: 固件文件路径

        Returns:
            (是否有效, 错误消息)
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False, f"固件文件不存在: {file_path}"

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return False, "固件文件为空"

            # 检查文件大小限制（例如：最大50MB）
            max_size = 50 * 1024 * 1024  # 50MB
            if file_size > max_size:
                return False, f"固件文件过大: {file_size} bytes，最大允许: {max_size} bytes"

            # 检查文件扩展名
            allowed_extensions = [".bin", ".hex", ".fw", ".img"]
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in allowed_extensions:
                return False, f"不支持的固件文件格式: {file_ext}"

            # 检查文件是否可读
            try:
                with open(file_path, "rb") as f:
                    f.read(1024)  # 尝试读取前1KB
            except Exception as e:
                return False, f"无法读取固件文件: {e}"

            return True, "固件文件验证通过"

        except Exception as e:
            return False, f"固件文件验证失败: {e}"

    @staticmethod
    def calculate_file_checksum(file_path: str, algorithm: str = "md5") -> Optional[str]:
        """
        计算文件校验和

        Args:
            file_path: 文件路径
            algorithm: 算法类型 (md5, sha1, sha256)

        Returns:
            校验和字符串或None
        """
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception as e:
            logger.error(f"计算文件校验和失败: {e}")
            return None

    @staticmethod
    def extract_version_from_filename(filename: str) -> str:
        """
        从文件名提取版本信息

        Args:
            filename: 文件名

        Returns:
            版本字符串
        """
        # 移除扩展名
        name_without_ext = os.path.splitext(filename)[0]

        # 尝试提取版本号模式 (v1.0.0, 1.0.0, etc.)
        import re

        version_patterns = [
            r"v?(\d+\.\d+\.\d+)",  # v1.0.0 或 1.0.0
            r"v?(\d+\.\d+)",  # v1.0 或 1.0
            r"v?(\d+)",  # v1 或 1
        ]

        for pattern in version_patterns:
            match = re.search(pattern, name_without_ext, re.IGNORECASE)
            if match:
                return match.group(1)

        # 如果没有找到版本号，返回文件名
        return name_without_ext


class DeviceValidator:
    """设备验证器"""

    @staticmethod
    def validate_device_list(device_ids: List[int]) -> Tuple[List[int], List[str]]:
        """
        验证设备列表

        Args:
            device_ids: 设备ID列表

        Returns:
            (有效设备ID列表, 错误消息列表)
        """
        valid_devices = []
        error_messages = []

        if not device_ids:
            error_messages.append("设备ID列表不能为空")
            return valid_devices, error_messages

        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    error_messages.append(f"设备 {device_id} 不存在")
                    continue

                # 检查设备状态（可以根据需要添加更多检查）
                valid_devices.append(device_id)

            except Exception as e:
                error_messages.append(f"验证设备 {device_id} 失败: {e}")

        return valid_devices, error_messages

    @staticmethod
    def check_device_compatibility(device_id: int, firmware_version: str) -> Tuple[bool, str]:
        """
        检查设备与固件的兼容性

        Args:
            device_id: 设备ID
            firmware_version: 固件版本

        Returns:
            (是否兼容, 消息)
        """
        try:
            device = Device.query.get(device_id)
            if not device:
                return False, f"设备 {device_id} 不存在"

            # 检查设备当前版本是否小于等于目标固件版本
            current_version = device.firmware_version
            # device_remark = device.device_remark
            
            if current_version == '未知':
                return True, f"设备 {device_id} 当前版本未知，兼容"

            # 比较版本号
            current_version = device.firmware_version.split(".")
            target_version = firmware_version.split(".")

            # 确保版本号格式正确
            if len(current_version) != 3 or len(target_version) != 3:
                return False, f"设备 {device_id} 的版本号格式不正确: {current_version}"

            # 将版本号转换为整数
            current_version_int = int(current_version[0]) << 16 | int(current_version[1]) << 8 | int(current_version[2])
            target_version_int = int(target_version[0]) << 16 | int(target_version[1]) << 8 | int(target_version[2])

            if current_version_int > target_version_int:
                return False, f"设备 {device_id} 已是最新版本 {firmware_version}"
            else:
                logger.info(f"设备 {device_id} 兼容固件版本 {firmware_version}")

            # 这里可以添加更复杂的兼容性检查逻辑
            # 例如：检查硬件版本、产品型号等
            return True, f"设备 {device_id} 兼容固件版本 {firmware_version}"

        except Exception as e:
            return False, f"检查设备兼容性失败: {e}"


class OtaTaskHelper:
    """OTA任务助手类"""

    @staticmethod
    def create_task_batch(device_ids: List[int], firmware_path: str, firmware_version: str) -> List[Dict]:
        """
        批量创建OTA任务数据

        Args:
            device_ids: 设备ID列表
            firmware_path: 固件路径
            firmware_version: 固件版本

        Returns:
            任务数据列表
        """
        tasks = []
        for device_id in device_ids:
            task_data = {
                "device_id": device_id,
                "firmware_path": firmware_path,
                "firmware_version": firmware_version,
                "status": "等待中",
                "progress": 0,
                "detailed_status": "等待中",
                "stage_info": "任务已创建，等待执行",
                "retry_count": 0,
                "max_retries": 3,
            }
            tasks.append(task_data)

        return tasks

    @staticmethod
    def calculate_task_priority(device_id: int, firmware_version: str) -> int:
        """
        计算任务优先级

        Args:
            device_id: 设备ID
            firmware_version: 固件版本

        Returns:
            优先级数字（越小优先级越高）
        """
        # 默认优先级
        priority = 5

        try:
            device = Device.query.get(device_id)
            if device:
                # 可以根据设备类型、重要性等调整优先级
                # 这里是示例逻辑
                if hasattr(device, "priority") and device.priority:
                    priority = device.priority
                elif hasattr(device, "device_type"):
                    # 重要设备优先级更高
                    if device.device_type in ["critical", "重要"]:
                        priority = 1
                    elif device.device_type in ["normal", "普通"]:
                        priority = 5
                    else:
                        priority = 9

        except Exception as e:
            logger.warning(f"计算任务优先级失败: {e}")

        return priority

    @staticmethod
    def format_task_summary(total_tasks: int, successful_tasks: int, failed_tasks: int) -> str:
        """
        格式化任务摘要信息

        Args:
            total_tasks: 总任务数
            successful_tasks: 成功任务数
            failed_tasks: 失败任务数

        Returns:
            格式化的摘要字符串
        """
        pending_tasks = total_tasks - successful_tasks - failed_tasks
        success_rate = (successful_tasks / total_tasks * 100) if total_tasks > 0 else 0

        summary = (
            f"任务总数: {total_tasks}, "
            f"成功: {successful_tasks}, "
            f"失败: {failed_tasks}, "
            f"待处理: {pending_tasks}, "
            f"成功率: {success_rate:.1f}%"
        )

        return summary


class FileManager:
    """文件管理器"""

    @staticmethod
    def save_uploaded_file(file, upload_folder: str, allowed_extensions: List[str] = None) -> Tuple[bool, str, str]:
        """
        保存上传的文件

        Args:
            file: 上传的文件对象
            upload_folder: 上传目录
            allowed_extensions: 允许的扩展名列表

        Returns:
            (是否成功, 文件路径, 错误消息)
        """
        try:
            if not file or not file.filename:
                return False, "", "没有选择文件"

            # 安全的文件名
            filename = secure_filename(file.filename)
            if not filename:
                return False, "", "无效的文件名"

            # 检查扩展名
            if allowed_extensions:
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext not in allowed_extensions:
                    return False, "", f"不支持的文件格式: {file_ext}"

            # 确保上传目录存在
            os.makedirs(upload_folder, exist_ok=True)

            # 生成唯一文件名（避免冲突）
            base_name, ext = os.path.splitext(filename)
            counter = 1
            final_filename = filename
            file_path = os.path.join(upload_folder, final_filename)

            while os.path.exists(file_path):
                final_filename = f"{base_name}_{counter}{ext}"
                file_path = os.path.join(upload_folder, final_filename)
                counter += 1

            # 保存文件
            file.save(file_path)

            return True, file_path, "文件保存成功"

        except Exception as e:
            return False, "", f"保存文件失败: {e}"

    @staticmethod
    def cleanup_old_files(directory: str, max_age_days: int = 30) -> int:
        """
        清理旧文件

        Args:
            directory: 目录路径
            max_age_days: 最大保留天数

        Returns:
            删除的文件数量
        """
        import time

        deleted_count = 0
        max_age_seconds = max_age_days * 24 * 3600
        current_time = time.time()

        try:
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    file_age = current_time - os.path.getmtime(file_path)
                    if file_age > max_age_seconds:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"删除旧文件: {file_path}")

        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")

        return deleted_count
