#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设备管理API测试脚本
用于验证新实现的分页和Ajax功能
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_device_api():
    """测试设备API功能"""
    base_url = "http://localhost:5000"
    
    print("开始测试设备管理API...")
    
    # 测试设备统计API
    print("\n1. 测试设备统计API")
    try:
        response = requests.get(f"{base_url}/api/devices/stats")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('stats', {})
                print(f"   ✓ 设备统计获取成功: 总数={stats.get('total', 0)}, 在线={stats.get('online', 0)}, 离线={stats.get('offline', 0)}")
            else:
                print(f"   ✗ 设备统计获取失败: {data.get('error', '未知错误')}")
        else:
            print(f"   ✗ 设备统计API请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ✗ 设备统计API测试异常: {e}")
    
    # 测试设备列表API
    print("\n2. 测试设备列表API")
    try:
        # 测试基本分页
        response = requests.get(f"{base_url}/api/devices?page=1&per_page=10")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                devices = data.get('devices', [])
                pagination = data.get('pagination', {})
                print(f"   ✓ 设备列表获取成功: 当前页={pagination.get('page', 0)}, 总页数={pagination.get('pages', 0)}, 设备数={len(devices)}")
                
                # 测试搜索功能
                if devices:
                    first_device = devices[0]
                    search_term = first_device.get('device_id', '')[:3]  # 取设备ID前3个字符
                    if search_term:
                        response = requests.get(f"{base_url}/api/devices?search={search_term}")
                        if response.status_code == 200:
                            search_data = response.json()
                            if search_data.get('success'):
                                search_devices = search_data.get('devices', [])
                                print(f"   ✓ 搜索功能测试成功: 搜索'{search_term}'找到{len(search_devices)}个设备")
                            else:
                                print(f"   ✗ 搜索功能测试失败: {search_data.get('error', '未知错误')}")
                
                # 测试状态筛选
                response = requests.get(f"{base_url}/api/devices?status=online")
                if response.status_code == 200:
                    filter_data = response.json()
                    if filter_data.get('success'):
                        online_devices = filter_data.get('devices', [])
                        print(f"   ✓ 状态筛选测试成功: 在线设备{len(online_devices)}个")
                    else:
                        print(f"   ✗ 状态筛选测试失败: {filter_data.get('error', '未知错误')}")
                        
            else:
                print(f"   ✗ 设备列表获取失败: {data.get('error', '未知错误')}")
        else:
            print(f"   ✗ 设备列表API请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ✗ 设备列表API测试异常: {e}")
    
    # 测试添加设备API
    print("\n3. 测试添加设备API")
    try:
        test_device = {
            "device_id": "TEST_DEVICE_001",
            "device_remark": "测试设备",
            "product_key": "TEST_PRODUCT_KEY"
        }
        
        response = requests.post(
            f"{base_url}/api/device/add",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(test_device)
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✓ 设备添加成功: {data.get('message', '')}")
                test_device_id = data.get('device', {}).get('id')
                
                # 测试删除设备API
                if test_device_id:
                    print("\n4. 测试删除设备API")
                    delete_response = requests.delete(f"{base_url}/api/device/delete/{test_device_id}")
                    if delete_response.status_code == 200:
                        delete_data = delete_response.json()
                        if delete_data.get('success'):
                            print(f"   ✓ 设备删除成功: {delete_data.get('message', '')}")
                        else:
                            print(f"   ✗ 设备删除失败: {delete_data.get('message', '未知错误')}")
                    else:
                        print(f"   ✗ 设备删除API请求失败: HTTP {delete_response.status_code}")
            else:
                print(f"   ✗ 设备添加失败: {data.get('message', '未知错误')}")
        else:
            print(f"   ✗ 设备添加API请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ✗ 设备添加API测试异常: {e}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    test_device_api()
