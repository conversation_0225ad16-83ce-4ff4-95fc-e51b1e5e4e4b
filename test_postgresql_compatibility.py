#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PostgreSQL兼容性测试脚本
测试应用在PostgreSQL环境下的各项功能
"""

import os
import sys
import time
from datetime import datetime, date, timedelta

# 设置环境变量使用调试数据库
os.environ['FLASK_ENV'] = 'development'
os.environ['DATABASE_URL'] = '****************************************************************/kfchargingdbg?options=-csearch_path%3Dkfchargingdbgc_schema'

def test_database_connection():
    """测试数据库连接和基本查询"""
    try:
        print("1. 测试数据库连接...")
        
        from app_factory import create_app
        from models.database import db
        from utils.database_utils import get_database_type, get_connection_info
        
        app = create_app()
        
        with app.app_context():
            # 检查数据库类型
            db_type = get_database_type()
            print(f"   数据库类型: {db_type}")
            
            # 获取连接信息
            conn_info = get_connection_info()
            print(f"   连接池大小: {conn_info.get('pool_size', 'N/A')}")
            print(f"   已检出连接: {conn_info.get('checked_out', 'N/A')}")
            
            # 测试基本查询
            from models.user import User
            user_count = User.query.count()
            print(f"   用户数量: {user_count}")
            
            return True
            
    except Exception as e:
        print(f"   ✗ 连接测试失败: {e}")
        return False

def test_crud_operations():
    """测试CRUD操作"""
    try:
        print("2. 测试CRUD操作...")
        
        from app_factory import create_app
        from models.database import db
        from models.user import User
        
        app = create_app()
        
        with app.app_context():
            # 测试查询
            users = User.query.limit(3).all()
            print(f"   查询用户: {len(users)} 个")
            
            # 测试创建（如果不存在测试用户）
            test_user = User.query.filter_by(username='test_pg_user').first()
            if not test_user:
                test_user = User(username='test_pg_user', email='<EMAIL>')
                test_user.set_password('test123')
                db.session.add(test_user)
                db.session.commit()
                print("   创建测试用户成功")
            else:
                print("   测试用户已存在")
            
            # 测试更新
            test_user.email = f'updated_{int(time.time())}@example.com'
            db.session.commit()
            print("   更新用户成功")
            
            # 测试删除（清理测试数据）
            db.session.delete(test_user)
            db.session.commit()
            print("   删除测试用户成功")
            
            return True
            
    except Exception as e:
        print(f"   ✗ CRUD测试失败: {e}")
        return False

def test_search_queries():
    """测试搜索查询"""
    try:
        print("3. 测试搜索查询...")
        
        from app_factory import create_app
        from models.database import db
        from models.device import Device
        from sqlalchemy import or_
        
        app = create_app()
        
        with app.app_context():
            # 测试LIKE查询
            devices = Device.query.filter(Device.device_id.like('%test%')).limit(5).all()
            print(f"   LIKE查询结果: {len(devices)} 个设备")
            
            # 测试contains查询
            devices = Device.query.filter(Device.device_id.contains('device')).limit(5).all()
            print(f"   contains查询结果: {len(devices)} 个设备")
            
            # 测试OR查询
            devices = Device.query.filter(
                or_(
                    Device.device_id.contains('test'),
                    Device.device_remark.contains('test')
                )
            ).limit(5).all()
            print(f"   OR查询结果: {len(devices)} 个设备")
            
            return True
            
    except Exception as e:
        print(f"   ✗ 搜索查询测试失败: {e}")
        return False

def test_date_queries():
    """测试日期查询"""
    try:
        print("4. 测试日期查询...")
        
        from app_factory import create_app
        from models.database import db
        from models.ota_task import OtaTask
        from utils.database_utils import date_filter
        
        app = create_app()
        
        with app.app_context():
            # 测试日期筛选
            today = date.today()
            yesterday = today - timedelta(days=1)
            
            # 使用兼容性日期函数
            tasks_today = OtaTask.query.filter(date_filter(OtaTask.created_at, today)).count()
            print(f"   今天的任务: {tasks_today} 个")
            
            tasks_yesterday = OtaTask.query.filter(date_filter(OtaTask.created_at, yesterday)).count()
            print(f"   昨天的任务: {tasks_yesterday} 个")
            
            # 测试日期范围查询
            week_ago = today - timedelta(days=7)
            start_datetime = datetime.combine(week_ago, datetime.min.time())
            end_datetime = datetime.combine(today, datetime.max.time())
            
            tasks_week = OtaTask.query.filter(
                db.and_(
                    OtaTask.created_at >= start_datetime,
                    OtaTask.created_at <= end_datetime
                )
            ).count()
            print(f"   本周的任务: {tasks_week} 个")
            
            return True
            
    except Exception as e:
        print(f"   ✗ 日期查询测试失败: {e}")
        return False

def test_join_queries():
    """测试连接查询"""
    try:
        print("5. 测试连接查询...")
        
        from app_factory import create_app
        from models.database import db
        from models.ota_task import OtaTask
        from models.device import Device
        
        app = create_app()
        
        with app.app_context():
            # 测试JOIN查询
            tasks_with_devices = OtaTask.query.join(Device).limit(10).all()
            print(f"   JOIN查询结果: {len(tasks_with_devices)} 个任务")
            
            # 测试复杂查询
            query = OtaTask.query.join(Device).filter(
                db.and_(
                    OtaTask.status == '成功',
                    Device.device_id.contains('device')
                )
            )
            
            successful_tasks = query.count()
            print(f"   复杂查询结果: {successful_tasks} 个成功任务")
            
            return True
            
    except Exception as e:
        print(f"   ✗ 连接查询测试失败: {e}")
        return False

def test_multithreading():
    """测试多线程数据库访问"""
    try:
        print("6. 测试多线程数据库访问...")
        
        import threading
        from app_factory import create_app
        from models.database import db
        from models.device import Device
        
        app = create_app()
        results = []
        errors = []
        
        def worker_thread(thread_id):
            try:
                with app.app_context():
                    # 每个线程执行一些数据库查询
                    devices = Device.query.limit(5).all()
                    count = Device.query.count()
                    results.append(f"线程{thread_id}: 查询到{len(devices)}个设备，总数{count}")
                    
                    # 清理会话
                    db.session.remove()
                    
            except Exception as e:
                errors.append(f"线程{thread_id}错误: {e}")
        
        # 创建多个线程
        threads = []
        for i in range(3):
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        if errors:
            print(f"   ✗ 多线程测试有错误: {errors}")
            return False
        else:
            print(f"   多线程测试成功:")
            for result in results:
                print(f"     {result}")
            return True
            
    except Exception as e:
        print(f"   ✗ 多线程测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    try:
        print("7. 测试查询性能...")
        
        from app_factory import create_app
        from models.database import db
        from models.device_parameter import DeviceParameter
        
        app = create_app()
        
        with app.app_context():
            # 测试大量数据查询性能
            start_time = time.time()
            params = DeviceParameter.query.limit(1000).all()
            end_time = time.time()
            
            query_time = end_time - start_time
            print(f"   查询1000条记录耗时: {query_time:.3f}秒")
            
            if query_time < 2.0:
                print("   性能良好")
                return True
            else:
                print("   性能较慢，可能需要优化")
                return True  # 仍然算作通过，只是性能警告
            
    except Exception as e:
        print(f"   ✗ 性能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("PostgreSQL兼容性测试")
    print("=" * 50)
    
    tests = [
        test_database_connection,
        test_crud_operations,
        test_search_queries,
        test_date_queries,
        test_join_queries,
        test_multithreading,
        test_performance,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("   ✓ 通过\n")
            else:
                print("   ✗ 失败\n")
        except Exception as e:
            print(f"   ✗ 异常: {e}\n")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有兼容性测试通过！PostgreSQL适配成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
