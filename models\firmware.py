
from datetime import datetime
from models.database import db

class Firmware(db.Model):
    """固件模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    version = db.Column(db.String(20), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    size = db.Column(db.Integer, nullable=False)  # 文件大小（字节）
    crc32 = db.Column(db.String(8), nullable=False)  # CRC32校验值
    description = db.Column(db.Text)
    upload_time = db.Column(db.DateTime, default=datetime.now)
    download_count = db.Column(db.Integer, default=0)