#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA数据库兼容性检查工具
检查数据库是否支持新的OTA字段
"""

import psycopg2
import sys
import os
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# PostgreSQL数据库配置
POSTGRESQL_CONFIGS = {
    'debug': {
        'host': '**************',
        'port': 5432,
        'database': 'kfchargingdbg',
        'user': 'KfChargingDbgC',
        'password': 'JT5WJ6Zn3hbAcWBz',
        'schema': 'kfchargingdbgc_schema'
    },
    'production': {
        'host': '**************',
        'port': 5432,
        'database': 'kafangcharging',
        'user': 'kafanglinlin',
        'password': '7jbWNHYZZLMa',
        'schema': 'kafanglinlin_schema'
    }
}

# 必需的新字段
REQUIRED_COLUMNS = [
    'detailed_status',
    'stage_info',
    'retry_count',
    'max_retries',
    'started_at',
    'completed_at'
]


class OtaCompatibilityChecker:
    """OTA兼容性检查器"""
    
    def __init__(self, config):
        self.config = config
        self.conn = None
        self.cursor = None
        self.schema = config['schema']
    
    def connect(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password'],
                sslmode='disable'
            )
            self.cursor = self.conn.cursor()
            
            # 设置搜索路径
            self.cursor.execute(f"SET search_path TO {self.schema}, public;")
            self.conn.commit()
            
            logger.info(f"成功连接到数据库: {self.config['database']}")
            return True
            
        except Exception as e:
            logger.error(f"连接数据库失败: {e}")
            return False
    
    def check_table_exists(self):
        """检查ota_task表是否存在"""
        try:
            self.cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = 'ota_task'
                )
            """, (self.schema,))
            
            exists = self.cursor.fetchone()[0]
            return exists
            
        except Exception as e:
            logger.error(f"检查表存在性失败: {e}")
            return False
    
    def get_table_columns(self):
        """获取表的所有列信息"""
        try:
            self.cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default 
                FROM information_schema.columns 
                WHERE table_schema = %s AND table_name = 'ota_task' 
                ORDER BY ordinal_position
            """, (self.schema,))
            
            columns = self.cursor.fetchall()
            return {col[0]: {
                'type': col[1], 
                'nullable': col[2], 
                'default': col[3]
            } for col in columns}
            
        except Exception as e:
            logger.error(f"获取列信息失败: {e}")
            return {}
    
    def check_compatibility(self):
        """检查兼容性"""
        try:
            logger.info(f"检查数据库 {self.config['database']} 的OTA兼容性...")
            
            # 检查表是否存在
            if not self.check_table_exists():
                logger.error("ota_task表不存在")
                return False, "表不存在"
            
            # 获取现有列
            existing_columns = self.get_table_columns()
            logger.info(f"现有列数量: {len(existing_columns)}")
            
            # 检查必需的新字段
            missing_columns = []
            existing_new_columns = []
            
            for column in REQUIRED_COLUMNS:
                if column in existing_columns:
                    existing_new_columns.append(column)
                else:
                    missing_columns.append(column)
            
            # 生成报告
            report = {
                'database': self.config['database'],
                'schema': self.schema,
                'table_exists': True,
                'total_columns': len(existing_columns),
                'existing_columns': list(existing_columns.keys()),
                'required_new_columns': REQUIRED_COLUMNS,
                'existing_new_columns': existing_new_columns,
                'missing_columns': missing_columns,
                'is_compatible': len(missing_columns) == 0,
                'needs_migration': len(missing_columns) > 0
            }
            
            # 打印报告
            self._print_report(report)
            
            return report['is_compatible'], report
            
        except Exception as e:
            logger.error(f"兼容性检查失败: {e}")
            return False, str(e)
    
    def _print_report(self, report):
        """打印检查报告"""
        print(f"\n{'='*60}")
        print(f"OTA兼容性检查报告 - {report['database']}")
        print(f"{'='*60}")
        print(f"数据库: {report['database']}")
        print(f"Schema: {report['schema']}")
        print(f"表存在: {'✓' if report['table_exists'] else '✗'}")
        print(f"总列数: {report['total_columns']}")
        
        print(f"\n现有列:")
        for col in report['existing_columns']:
            print(f"  - {col}")
        
        print(f"\n必需的新字段:")
        for col in report['required_new_columns']:
            status = '✓' if col in report['existing_new_columns'] else '✗'
            print(f"  {status} {col}")
        
        if report['missing_columns']:
            print(f"\n缺失的字段:")
            for col in report['missing_columns']:
                print(f"  ✗ {col}")
        
        print(f"\n兼容性状态: {'✓ 兼容' if report['is_compatible'] else '✗ 需要迁移'}")
        
        if report['needs_migration']:
            print(f"\n建议操作:")
            print(f"  1. 执行SQL迁移脚本: sql_tools/ota_task_migration.sql")
            print(f"  2. 或运行Python迁移脚本: python sql_tools/ota_task_auto_migration.py")
    
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()


def main():
    """主函数"""
    logger.info("开始OTA数据库兼容性检查")
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        env = sys.argv[1]
        if env not in POSTGRESQL_CONFIGS:
            logger.error(f"无效的环境参数: {env}，支持的环境: {list(POSTGRESQL_CONFIGS.keys())}")
            return False
        environments = [env]
    else:
        # 检查所有环境
        environments = list(POSTGRESQL_CONFIGS.keys())
    
    all_compatible = True
    reports = {}
    
    for env in environments:
        logger.info(f"\n检查 {env} 环境...")
        
        config = POSTGRESQL_CONFIGS[env]
        checker = OtaCompatibilityChecker(config)
        
        try:
            if checker.connect():
                is_compatible, report = checker.check_compatibility()
                reports[env] = report
                if not is_compatible:
                    all_compatible = False
            else:
                logger.error(f"{env} 环境连接失败")
                all_compatible = False
        
        except Exception as e:
            logger.error(f"{env} 环境检查异常: {e}")
            all_compatible = False
        
        finally:
            checker.close()
    
    # 总结报告
    print(f"\n{'='*60}")
    print("总体兼容性报告")
    print(f"{'='*60}")
    
    for env, report in reports.items():
        if isinstance(report, dict):
            status = '✓ 兼容' if report['is_compatible'] else '✗ 需要迁移'
            print(f"{env:12}: {status}")
        else:
            print(f"{env:12}: ✗ 检查失败")
    
    if all_compatible:
        print(f"\n✓ 所有环境都兼容新的OTA系统")
    else:
        print(f"\n✗ 部分环境需要数据库迁移")
        print(f"\n建议执行以下步骤:")
        print(f"1. 先在测试环境执行迁移脚本")
        print(f"2. 验证测试环境功能正常")
        print(f"3. 再在生产环境执行迁移脚本")
    
    return all_compatible


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
