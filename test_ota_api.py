#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA任务API测试脚本
用于验证新实现的API功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app_factory import create_app
from models.database import db
from models.ota_task import OtaTask
from models.device import Device
from models.firmware import Firmware
from datetime import datetime

def test_ota_api():
    """测试OTA任务API"""
    app = create_app()
    
    with app.app_context():
        print("=== OTA任务API测试 ===")
        
        # 测试1: 检查数据库连接
        try:
            total_tasks = OtaTask.query.count()
            print(f"✅ 数据库连接正常，当前任务总数: {total_tasks}")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return
        
        # 测试2: 检查任务统计
        try:
            success_count = OtaTask.query.filter_by(status='成功').count()
            failed_count = OtaTask.query.filter_by(status='失败').count()
            in_progress_count = OtaTask.query.filter_by(status='进行中').count()
            waiting_count = OtaTask.query.filter_by(status='等待中').count()
            
            print(f"✅ 任务统计:")
            print(f"   - 成功: {success_count}")
            print(f"   - 失败: {failed_count}")
            print(f"   - 进行中: {in_progress_count}")
            print(f"   - 等待中: {waiting_count}")
        except Exception as e:
            print(f"❌ 任务统计失败: {e}")
        
        # 测试3: 检查分页查询
        try:
            query = OtaTask.query.join(Device).order_by(OtaTask.created_at.desc())
            pagination = query.paginate(page=1, per_page=20, error_out=False)
            
            print(f"✅ 分页查询正常:")
            print(f"   - 当前页: {pagination.page}")
            print(f"   - 总页数: {pagination.pages}")
            print(f"   - 每页数量: {pagination.per_page}")
            print(f"   - 总记录数: {pagination.total}")
            print(f"   - 当前页记录数: {len(pagination.items)}")
            
            # 显示前几条记录
            for i, task in enumerate(pagination.items[:3]):
                print(f"   - 任务{i+1}: ID={task.id}, 设备={task.device.device_id}, 状态={task.status}")
                
        except Exception as e:
            print(f"❌ 分页查询失败: {e}")
        
        # 测试4: 检查路由注册
        try:
            with app.test_client() as client:
                # 测试任务列表页面
                response = client.get('/ota/tasks')
                print(f"✅ 任务列表页面: {response.status_code}")
                
                # 测试API端点（需要登录，所以会返回重定向）
                response = client.get('/api/ota/tasks')
                print(f"✅ 任务API端点: {response.status_code}")
                
                response = client.get('/api/ota/tasks/stats')
                print(f"✅ 统计API端点: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 路由测试失败: {e}")
        
        print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_ota_api()
