#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
并行OTA任务管理器
支持多设备OTA任务的并行执行
"""

import threading
import time
from concurrent.futures import ThreadPoolExecutor, Future
from queue import PriorityQueue, Empty
from typing import Dict, Optional, Callable, Any
from dataclasses import dataclass, field
from datetime import datetime
from utils.logger import LoggerManager
from services.ota_task_state import OtaTaskStateManager, OtaTaskDetailedStatus
from services.database_session_manager import db_session_manager, thread_safe_db_ops

# 获取日志记录器
logger = LoggerManager.get_logger()


@dataclass
class OtaTaskItem:
    """OTA任务项"""
    task_id: int
    priority: int
    task_func: Callable
    args: tuple
    kwargs: dict
    created_at: datetime = field(default_factory=datetime.now)
    
    def __lt__(self, other):
        """优先级比较（数字越小优先级越高）"""
        return self.priority < other.priority


class ParallelOtaTaskManager:
    """并行OTA任务管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialize()
            return cls._instance
    
    def _initialize(self):
        """初始化管理器"""
        # 配置参数
        self.max_workers = 5  # 最大并发任务数
        self.queue_timeout = 1.0  # 队列超时时间
        
        # 核心组件
        self.task_queue: PriorityQueue[OtaTaskItem] = PriorityQueue()
        self.executor: Optional[ThreadPoolExecutor] = None
        self.running_tasks: Dict[int, Future] = {}
        self.task_states: Dict[int, OtaTaskStateManager] = {}
        
        # 控制变量
        self.is_running = False
        self.shutdown_event = threading.Event()
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'active_tasks': 0
        }
        
        logger.info("并行OTA任务管理器初始化完成")
    
    def start(self, app=None):
        """启动任务管理器"""
        if self.is_running:
            logger.warning("任务管理器已在运行中")
            return
        
        # 初始化数据库会话管理器
        if app:
            db_session_manager.initialize_with_app(app)
        
        # 创建线程池
        self.executor = ThreadPoolExecutor(
            max_workers=self.max_workers,
            thread_name_prefix="OTA-Worker"
        )
        
        # 启动监控线程
        self.is_running = True
        self.shutdown_event.clear()
        self.monitor_thread = threading.Thread(
            target=self._monitor_tasks,
            name="OTA-Monitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info(f"并行OTA任务管理器已启动，最大并发数: {self.max_workers}")
    
    def stop(self, timeout: float = 30.0):
        """停止任务管理器"""
        if not self.is_running:
            return
        
        logger.info("正在停止并行OTA任务管理器...")
        
        # 设置停止标志
        self.is_running = False
        self.shutdown_event.set()
        
        # 等待监控线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=True, timeout=timeout)
        
        # 清理资源
        self.running_tasks.clear()
        self.task_states.clear()
        
        # 关闭数据库会话
        db_session_manager.close_all_sessions()
        
        logger.info("并行OTA任务管理器已停止")
    
    def submit_task(self, task_id: int, task_func: Callable, 
                   priority: int = 5, *args, **kwargs) -> bool:
        """提交OTA任务"""
        if not self.is_running:
            logger.error("任务管理器未运行，无法提交任务")
            return False
        
        if task_id in self.running_tasks or task_id in self.task_states:
            logger.warning(f"任务 {task_id} 已存在，跳过提交")
            return False
        
        # 创建任务项
        task_item = OtaTaskItem(
            task_id=task_id,
            priority=priority,
            task_func=task_func,
            args=args,
            kwargs=kwargs
        )
        
        # 创建任务状态管理器
        state_manager = OtaTaskStateManager(task_id)
        self.task_states[task_id] = state_manager
        
        # 加入队列
        self.task_queue.put(task_item)
        self.stats['total_tasks'] += 1
        
        logger.info(f"任务 {task_id} 已提交到队列，优先级: {priority}")
        return True
    
    def cancel_task(self, task_id: int) -> bool:
        """取消任务"""
        # 取消正在运行的任务
        if task_id in self.running_tasks:
            future = self.running_tasks[task_id]
            if future.cancel():
                logger.info(f"任务 {task_id} 已取消")
                self._cleanup_task(task_id, cancelled=True)
                return True
            else:
                logger.warning(f"任务 {task_id} 无法取消（可能已在执行中）")
                return False
        
        # 更新任务状态
        if task_id in self.task_states:
            self.task_states[task_id].set_status(
                OtaTaskDetailedStatus.CANCELLED, "任务已被取消"
            )
            self._update_task_in_db(task_id)
        
        return True
    
    def pause_task(self, task_id: int) -> bool:
        """暂停任务（预留接口）"""
        logger.info(f"收到暂停任务请求: {task_id}")
        logger.info("暂停功能正在开发中，当前仅记录请求")
        
        # 更新任务状态为暂停
        if task_id in self.task_states:
            self.task_states[task_id].set_status(
                OtaTaskDetailedStatus.PAUSED, "任务已暂停"
            )
            self._update_task_in_db(task_id)
            return True
        
        return False
    
    def get_task_status(self, task_id: int) -> Optional[dict]:
        """获取任务状态"""
        if task_id in self.task_states:
            return self.task_states[task_id].to_dict()
        return None
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        self.stats['active_tasks'] = len(self.running_tasks)
        return self.stats.copy()
    
    def _monitor_tasks(self):
        """监控任务执行"""
        logger.info("任务监控线程已启动")
        
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # 处理队列中的任务
                self._process_queue()
                
                # 检查完成的任务
                self._check_completed_tasks()
                
                # 短暂休眠
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"任务监控异常: {e}")
                time.sleep(1.0)
        
        logger.info("任务监控线程已退出")
    
    def _process_queue(self):
        """处理任务队列"""
        # 检查是否有空闲的工作线程
        if len(self.running_tasks) >= self.max_workers:
            return
        
        try:
            # 从队列获取任务
            task_item = self.task_queue.get(timeout=self.queue_timeout)
            
            # 提交任务到线程池
            future = self.executor.submit(
                self._execute_task_wrapper,
                task_item
            )
            
            # 记录运行中的任务
            self.running_tasks[task_item.task_id] = future
            
            logger.info(f"任务 {task_item.task_id} 已开始执行")
            
        except Empty:
            # 队列为空，继续监控
            pass
        except Exception as e:
            logger.error(f"处理任务队列异常: {e}")
    
    def _check_completed_tasks(self):
        """检查已完成的任务"""
        completed_tasks = []
        
        for task_id, future in self.running_tasks.items():
            if future.done():
                completed_tasks.append(task_id)
        
        # 清理已完成的任务
        for task_id in completed_tasks:
            self._cleanup_task(task_id)
    
    def _execute_task_wrapper(self, task_item: OtaTaskItem):
        """任务执行包装器"""
        task_id = task_item.task_id
        
        try:
            # 获取任务状态管理器
            state_manager = self.task_states.get(task_id)
            if not state_manager:
                logger.error(f"任务 {task_id} 状态管理器不存在")
                return
            
            # 开始任务
            state_manager.start_task()
            self._update_task_in_db(task_id)
            
            # 执行任务
            result = task_item.task_func(*task_item.args, **task_item.kwargs)
            
            # 完成任务
            state_manager.complete_task(True, "任务执行成功")
            self._update_task_in_db(task_id)
            self.stats['completed_tasks'] += 1
            
            logger.info(f"任务 {task_id} 执行成功")
            
        except Exception as e:
            logger.error(f"任务 {task_id} 执行失败: {e}")
            
            # 更新失败状态
            if task_id in self.task_states:
                self.task_states[task_id].complete_task(False, str(e))
                self._update_task_in_db(task_id)
            
            self.stats['failed_tasks'] += 1
    
    def _cleanup_task(self, task_id: int, cancelled: bool = False):
        """清理任务"""
        # 从运行列表中移除
        self.running_tasks.pop(task_id, None)
        
        # 更新状态
        if cancelled and task_id in self.task_states:
            self.task_states[task_id].set_status(
                OtaTaskDetailedStatus.CANCELLED, "任务已取消"
            )
            self._update_task_in_db(task_id)
        
        logger.debug(f"任务 {task_id} 已清理")
    
    def _update_task_in_db(self, task_id: int):
        """更新数据库中的任务状态"""
        if task_id not in self.task_states:
            return
        
        state_manager = self.task_states[task_id]
        
        # 更新任务状态
        thread_safe_db_ops.update_task_status(
            task_id=task_id,
            status=state_manager.get_legacy_status(),
            progress=state_manager.progress.percentage,
            error_message=str(state_manager.error) if state_manager.error else None,
            detailed_status=state_manager.status.value,
            stage_info=state_manager.progress.message
        )


# 创建全局实例
parallel_ota_manager = ParallelOtaTaskManager()


def configure_parallel_ota_manager(max_workers: int = 5):
    """配置并行OTA管理器参数"""
    parallel_ota_manager.max_workers = max_workers
    logger.info(f"并行OTA管理器配置更新: max_workers={max_workers}")
